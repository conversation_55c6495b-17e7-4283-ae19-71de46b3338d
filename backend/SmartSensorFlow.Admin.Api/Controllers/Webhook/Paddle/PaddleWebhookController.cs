using System.Security.Cryptography;
using System.Text;
using System.Threading.Channels;
using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Integration.Paddle.Config;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Webhook;

[Route("api/billing")]
public class PaddleWebhookController(
    ILogger<PaddleWebhookController> logger,
    PaddleConfiguration paddleConfiguration,
    Channel<PaddleWebhookMessage> queue) : ControllerBase
{
    [HttpPost("webhook")]
    public async Task<IActionResult> HandleWebhook()
    {
        try
        {
            Request.EnableBuffering();

            // 1. Get Paddle-Signature header
            if (!Request.Headers.TryGetValue("Paddle-Signature", out var paddleSignature) || string.IsNullOrEmpty(paddleSignature))
            {
                logger.LogError("Paddle-Signature not present in request headers");
                return BadRequest(new { error = "Invalid request" });
            }

            var secretKey = paddleConfiguration.NotificationSecretKey;
            if (string.IsNullOrEmpty(secretKey))
            {
                logger.LogError("Secret key not defined");
                return StatusCode(500, new { error = "Server misconfigured" });
            }

            // 2. Extract timestamp and signature from header
            var parts = paddleSignature.ToString().Split(";");
            if (parts.Length < 2)
            {
                logger.LogError("Invalid Paddle-Signature format");
                return BadRequest(new { error = "Invalid request" });
            }

            var timestamp = parts[0].Split("=")[1];
            var signature = parts[1].Split("=")[1];

            // (Optional) Check timestamp against current time and reject if it's over 5 seconds old
            if (!long.TryParse(timestamp, out var timestampInt))
            {
                logger.LogError("Invalid timestamp format");
                return BadRequest(new { error = "Invalid request" });
            }

            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var eventTime = timestampInt * 1000; // Convert seconds to milliseconds

            if (currentTime - eventTime > 5000)
            {
                logger.LogError("Webhook event expired (timestamp is over 5 seconds old):");
                logger.LogError("Event time: {0}", eventTime);
                logger.LogError("Current time: {0}", currentTime);
                return StatusCode(408, new { error = "Event expired" });
            }

            // We need `raw` request body to validate the integrity. Use StreamReader to ensure the request body isn't converted to JSON.
            using var reader = new StreamReader(Request.Body, Encoding.UTF8);
            var bodyRaw = await reader.ReadToEndAsync();
            Request.Body.Position = 0;

            // 3. Build signed payload
            var signedPayload = $"{timestamp}:{bodyRaw}";

            // 4. Hash signed payload using HMAC SHA256 and the secret key
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
            var computedHash = hmac.ComputeHash(Encoding.UTF8.GetBytes(signedPayload));
            var computedSignature = BitConverter.ToString(computedHash).Replace("-", "").ToLower();

            // 5. Compare signatures
            if (!CryptographicOperations.FixedTimeEquals(Encoding.UTF8.GetBytes(computedSignature), Encoding.UTF8.GetBytes(signature)))
            {
                logger.LogError("Computed signature does not match Paddle signature");
                return Unauthorized(new { error = "Invalid signature" });
            }

            // 6. Process the webhook event
            await queue.Writer.WriteAsync(new PaddleWebhookMessage()
            {
                RawBody = bodyRaw,
            });

            return Ok(new { success = true });
        }
        catch (Exception ex)
        {
            logger.LogError("Failed to verify and process Paddle webhook {message}", ex.Message);
            return StatusCode(500, new { error = "Failed to verify and process webhook" });
        }
    }
}