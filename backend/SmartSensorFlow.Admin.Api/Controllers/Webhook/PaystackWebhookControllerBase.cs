using System.Threading.Channels;
using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.IOC;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Api.Controllers.Webhook;

public abstract class PaystackWebhookControllerBase(
    IPaymentServiceResolver paymentServiceResolver,
    Channel<PaystackWebhookMessage> queue) : ProblemDetailsControllerBase
{
    protected abstract ProductType GetProductType();

    protected async Task<IActionResult> DispatchEvent()
    {
        var headerValue = Request.Headers["x-paystack-signature"].ToString();

        if (headerValue is null)
        {
            // Valid header not present - maybe log it
            Console.Error.WriteLine("Verification header not present");
            return Ok();
        }

        var streamReader = new StreamReader(Request.Body);
        var body = await streamReader.ReadToEndAsync();

        if (body is null)
        {
            Console.Error.WriteLine("No body in request");
            // No body in response - so log this somewhere
            return Ok();
        }

        if (!paymentServiceResolver[GetProductType()].VerifyWebhookEvent(headerValue, body))
        {
            // Failed to verify request to webhook
            Console.Error.WriteLine("Signatures do not match");
            return Ok();
        }

        await queue.Writer.WriteAsync(new PaystackWebhookMessage()
        {
            RawBody = body,
            ProductType = GetProductType(),
        });

        return Ok();
    }
}