using System.Threading.Channels;
using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.IOC;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Api.Controllers.Webhook;

[Route("api/webhook/paystack")]
public class PaystackPlatformWebhookController(
    IPaymentServiceResolver paymentServiceResolver,
    Channel<PaystackWebhookMessage> queue) : PaystackWebhookControllerBase(paymentServiceResolver, queue)
{
    [HttpPost("platform")]
    public async Task<IActionResult> HandlePlatformWebhook()
    {
        return await DispatchEvent();
    }

    protected override ProductType GetProductType()
    {
        return ProductType.Platform;
    }
}
