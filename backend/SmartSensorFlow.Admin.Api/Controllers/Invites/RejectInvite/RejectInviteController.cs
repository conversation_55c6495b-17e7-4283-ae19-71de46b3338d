using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Organisations.Users.RejectInvite.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Data.DataServices.Services;

namespace SmartSensorFlow.Admin.Api.Controllers.Invites.RejectInvite;

[Route("api/invite")]
[ApiController]
public class RejectInviteController(
    IQueryHandler queryHandler,
    IUserService userService,
    IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("reject")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Reject(RejectInviteRequest request)
    {
        var userTask = userService.GetUserById(requestContextProvider.UserId!.Value);
        var inviteTask = queryHandler.GetInviteByInviteKey(request.InviteKey);

        await Task.WhenAll(userTask, inviteTask);
        var user = userTask.Result;
        var invite = inviteTask.Result;

        if (user is null)
        {
            return Unauthorized();
        }

        if (invite is null)
        {
            return BadRequest("Invalid invite key provided");
        }

        // Ensure that this user can accept this invite
        if (invite.Email != user.EmailAddress)
        {
            return BadRequest($"Invite is intended for {invite.Email} but {user.EmailAddress} is trying to reject it");
        }

        if (invite.InviteStatus != Common.Enums.InviteStatus.Pending)
        {
            return BadRequest("Invite has already been accepted or rejected");
        }

        if (invite.InviteExpireDate < DateTime.Now)
        {
            return BadRequest("Invite to organisation has already expired.");
        }

        // Reject this invite
        await queryHandler.RejectInvite(request.InviteKey, request.RejectReason);

        // Did the user block this invite?
        if (request.IsBlocked)
        {
            // Existing user?
            if (requestContextProvider.UserId is not null)
            {
                // Yes, so link in InviteBlocks block
                await queryHandler.BlockAsExistingUser(requestContextProvider.UserId.Value, invite.OrganisationId, request.BlockReason);
            }
            else
            {
                // No, so link in EmailInviteBlocks
                await queryHandler.BlockAsNonExistingUser(invite.Email, invite.OrganisationId, request.BlockReason);
            }
        }

        return Ok();
    }
}
