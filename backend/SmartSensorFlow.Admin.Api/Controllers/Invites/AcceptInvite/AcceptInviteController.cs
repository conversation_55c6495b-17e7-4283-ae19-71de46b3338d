using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Invites.AcceptInvite.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Data.DataServices.Services;

namespace SmartSensorFlow.Admin.Api.Controllers.Invites.AcceptInvite;

[Route("api/invite")]
[ApiController]
public class AcceptInviteController(
    IQueryHandler queryHandler,
    IUserService userService,
    IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("accept")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Accept(AcceptInviteRequest request)
    {
        var userTask = userService.GetUserById(requestContextProvider.UserId!.Value);
        var inviteTask = queryHandler.GetInviteByInviteKey(request.InviteKey);

        await Task.WhenAll(userTask, inviteTask);
        var user = userTask.Result;
        var invite = inviteTask.Result;

        if (user is null)
        {
            return Unauthorized();
        }

        if (invite is null)
        {
            return BadRequest("Invalid invite key provided");
        }

        if (invite.InviteStatus != Common.Enums.InviteStatus.Pending)
        {
            return BadRequest("Invite has already been accepted or rejected");
        }

        if (invite.InviteExpireDate < DateTime.Now)
        {
            return BadRequest("Invite to organisation has already expired.");
        }

        // Finally accept the invite and add user to this organisation
        await queryHandler.AcceptInvite(request.InviteKey);

        // Add this user to the organisation specified in the invite
        await queryHandler.AddUserToOrganisation(invite.OrganisationId, requestContextProvider.UserId!.Value);

        return Ok();
    }
}
