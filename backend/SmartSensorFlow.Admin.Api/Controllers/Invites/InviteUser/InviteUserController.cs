using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Configuration;
using SmartSensorFlow.Admin.Api.Controllers.Invites.InviteUser.Models;
using SmartSensorFlow.Admin.Api.Controllers.Organisations.Users.InviteUser.Models;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Data.DataServices.Services;
using SmartSensorFlow.Admin.Integration.EmailJS;
using SmartSensorFlow.Admin.Security;

namespace SmartSensorFlow.Admin.Api.Controllers.Invites.InviteUser;

[Route("api/invite")]
[ApiController]
public class InviteUserToOrganisationController(
    IQueryHandler queryHandler,
    IUserService userService,
    IEmailService emailService,
    IRequestContextProvider requestContextProvider,
    InviteConfiguration inviteConfiguration) : ProblemDetailsControllerBase
{
    [HttpPost("organisation/{organisationId}")]
    [ProducesResponseType(typeof(InviteUserResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Invite(int organisationId, InviteUserRequest request)
    {
        // Get the user associated with this email
        var userTask = userService.GetUserByEmail(request.EmailAddress);
        var organisationTask = queryHandler.GetOrganisationById(organisationId);

        await Task.WhenAll(userTask, organisationTask);

        var user = userTask.Result;
        var organisation = organisationTask.Result;

        if (organisation is null)
        {
            return BadRequest("Invalid organisation ID");
        }

        if (user is not null)
        {
            // Is this user already in this org?
            if (await queryHandler.IsUserInOrganisation(user.Id, organisationId))
            {
                return Ok(new InviteUserResponse() { Success = false, FailureReason = $"User {request.EmailAddress} is already in organisation {organisationId}" });
            }

            // Did this user block this organisation from inviting them?
            var inviteBlock = await queryHandler.UserHasBlockedInvitesById(user.Id, organisationId);
            if (inviteBlock is not null)
            {
                return Ok(new InviteUserResponse() { Success = false, FailureReason = $"User {request.EmailAddress} has a blocked this organisation from sending them invites - Reason {inviteBlock.Reason}" });
            }
        }
        else
        {
            // Did this user block this organisation from inviting them?
            var inviteBlock = await queryHandler.UserHasBlockedInvitesByEmail(request.EmailAddress, organisationId);
            if (inviteBlock is not null)
            {
                return Ok(new InviteUserResponse() { Success = false, FailureReason = $"User {request.EmailAddress} has a blocked this organisation from sending them invites - Reason {inviteBlock.Reason}" });
            }
        }

        // Does this user have pending invites for this organisation?
        // Get only organisation invites
        var invites = await queryHandler.GetInviteByEmailAddress(request.EmailAddress);

        if (invites.Where(x => x.InviteType == InviteType.Organisation
            && x.OrganisationId == organisationId
            && x.InviteExpireDate > DateTime.Now).Any())
        {
            return Ok(new InviteUserResponse() { Success = false, FailureReason = $"User {request.EmailAddress} has a pending invite from organisation {organisationId}" });
        }

        var inviteKey = PinGenerator.GenerateAlphabetical();

        // Create invite
        await queryHandler.CreateOrganisationInvite(
            inviteKey,
            request.EmailAddress,
            organisationId,
            requestContextProvider.UserId!.Value,
            user?.FirstName ?? request.FirstName,
            user?.LastName ?? request.Surname);

        // Send email
        if (user is null)
        {
            await emailService.SendInviteEmail(request.FirstName, request.Surname, organisation.Name, request.EmailAddress, $"{string.Format(inviteConfiguration.Identity, inviteKey)}");
        }
        else
        {
            await emailService.SendInviteEmail(user.FirstName, user.LastName, organisation.Name, user.EmailAddress, $"{inviteConfiguration.Admin}");
        }
        return Ok(new InviteUserResponse() { Success = true });
    }
}