using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Modules.GetModules.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Modules.GetModules;

[Route("api/modules")]
[ApiController]
public class GetModulesController(IQueryHandler queryHandler) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(GetModuleResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Get()
    {
        var modules = await queryHandler.GetModules();
        return Ok(modules.ToResponse());
    }
}
