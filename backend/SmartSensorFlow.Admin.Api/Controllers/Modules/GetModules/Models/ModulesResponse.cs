using System;

namespace SmartSensorFlow.Admin.Api.Controllers.Modules.GetModules.Models;

public class ModulesResponse
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string Description { get; set; }

    public required ModuleAttribute Attributes { get; set; }

    public required string CreatedBy { get; set; }

    public required DateTimeOffset CreatedAt { get; set; }

    public required string ModifiedBy { get; set; }

    public required DateTimeOffset ModifiedAt { get; set; }
}
