using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Roles.GetRoles.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Roles.GetRoles;

[Route("api/role")]
[ApiController]
public class GetRolesController(IQueryHandler queryHandler) : ControllerBase
{
    [HttpGet("organisation/{organisationId}")]
    [ProducesResponseType(typeof(GetRolesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Get(int organisationId)
    {
        var roles = await queryHandler.GetOrganisationRoles(organisationId);

        return Ok(roles.ToResponse());
    }
}
