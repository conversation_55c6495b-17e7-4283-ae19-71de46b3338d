using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Roles.Permissions.AddPermissionToRole.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Roles.Permissions.AddPermissionToRole;

[Route("api/role")]
[ApiController]
public class AddPermissionToRoleController(
    IQueryHandler queryHandler,
    IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("permission/add")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> AddPermission(AddPermissionToRoleRequest request)
    {
        // Is this permission already assigned to this role?
        var permissions = await queryHandler.GetPermissionsOfRole(request.RoleId);

        if (permissions.Any(x => x.Id == request.PermissionId))
        {
            return BadRequest($"Permission {permissions.Single(x => x.Id == request.PermissionId).PermissionName} is already assigned to this role.");
        }

        // Assign permission to Role
        await queryHandler.AssignPermissionToRole(
            request.RoleId,
            request.PermissionId,
            requestContextProvider.UserId!.Value);

        return Ok();
    }
}
