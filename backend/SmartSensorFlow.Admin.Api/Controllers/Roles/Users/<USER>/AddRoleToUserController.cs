using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Roles.Users.AddRoleToUser.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Roles.Users.AddRoleToUser;

[Route("api/role")]
[ApiController]
public class AddRoleToUserController(
    IQueryHandler queryHandler,
    IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("user/add")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> AddPermission(AddRoleToUserRequest request)
    {
        // Does this user already have this role?
        var roles = await queryHandler.GetRolesOfUser(request.UsersId);

        if (roles.Any(x => x.Id == request.RoleId))
        {
            return BadRequest($"Permission {roles.Single(x => x.Id == request.RoleId).RoleName} is already assigned to this user.");
        }

        // Assign user to role
        await queryHandler.AssignRoleToUser(
            request.UsersId,
            request.RoleId,
            requestContextProvider.UserId!.Value);

        return Ok();
    }
}
