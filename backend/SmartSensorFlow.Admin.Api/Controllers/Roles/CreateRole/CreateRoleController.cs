using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Roles.CreateRole.Request;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Roles.CreateRole;

[Route("api/role")]
[ApiController]
public class CreateRoleController(
    IQueryHandler queryHandler,
    IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("create")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Create(CreateRoleRequest request)
    {
        var userId = requestContextProvider.UserId;
        await queryHandler.CreateRole(request.RoleName, request.RoleDescription, request.OrganisationId, userId!.Value);
        return Ok();
    }
}
