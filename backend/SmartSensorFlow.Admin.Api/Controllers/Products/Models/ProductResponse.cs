using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Products.Models;

public class ProductResponse
{
    public required int Id { get; set; }

    public required string PlanID { get; set; }

    public required string Name { get; set; }

    public required string Price { get; set; }

    public required ProductType Type { get; set; }

    public required string Description { get; set; }

    public required ProductAttributesBase Attributes { get; set; }
}
