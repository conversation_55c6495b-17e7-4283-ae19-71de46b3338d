namespace SmartSensorFlow.Admin.Api.Controllers.Products.Models;

public class ProductAttributesSupport : ProductAttributesBase
{
    public required List<string> SupportChannels { get; set; }

    public required string ResponseSLA { get; set; }

    public required bool KnowledgeBaseAccess { get; set; }

    public required string TechnicalConsultation { get; set; }

    public required bool ProactiveMonitoring { get; set; }

    public required bool BusinessReviews { get; set; }

    public required bool OnDemandTraining { get; set; }

    public required bool DedicatedAccountManager { get; set; }
}
