using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Permissions.Response;

public class PermissionResponse
{
    public required int Id { get; set; }

    public required string PermissionName { get; set; }

    public required string PermissionDescription { get; set; }

    public required PermissionScope Scope { get; set; }

    public required PermissionDomain Domain { get; set; }
}
