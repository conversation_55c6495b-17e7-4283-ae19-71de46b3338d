using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Permissions.Response;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices.Services;

namespace SmartSensorFlow.Admin.Api.Controllers.Permissions;

[Route("api/permissions")]
[ApiController]
public class GetAllPermissionsController(IPermissionService permissionService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(PermissionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Get()
    {
        var result = await permissionService.GetAllPermissions();
        return Ok(result.ToResponse());
    }
}
