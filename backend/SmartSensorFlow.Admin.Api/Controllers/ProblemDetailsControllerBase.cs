using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Errors;

namespace SmartSensorFlow.Admin.Api.Controllers;

public abstract class ProblemDetailsControllerBase : ControllerBase
{
    protected ObjectResult NotAuthorizedProblem(string detail)
    {
        var result = Problem(detail, Request.Path.Value, 401, "Not Authorized");
        result.ContentTypes.Add("application/json");
        return result;
    }

    protected ObjectResult ValidationProblem(string detail, List<ValidationErrors> additionalErrors)
    {
        var problemDetails = new ProblemDetails()
        {
            Detail = detail,
            Instance = Request.Path.Value,
            Status = 400,
            Title = "Data validation issue",
            Extensions = new Dictionary<string, object?>() { { "validationIssues", additionalErrors } }
        };

        var result = BadRequest(problemDetails);
        result.ContentTypes.Add("application/json");
        return result;
    }

    protected ObjectResult NotFoundProblem(string detail)
    {
        var problemDetails = new ProblemDetails()
        {
            Detail = detail,
            Instance = Request.Path.Value,
            Status = 404,
            Title = "Not found",
        };

        var result = NotFound(problemDetails);
        result.ContentTypes.Add("application/json");
        return result;
    }
}
