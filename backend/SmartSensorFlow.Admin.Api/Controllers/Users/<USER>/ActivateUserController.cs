using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Users.ActivateUser.Models;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Users.ActivateUser;

[Route("api/user")]
[ApiController]
public class ActivateUserController(IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpPost("organisation/{organisationId}/activate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Activate(int organisationId, ActivateUserRequest request)
    {
        // Is this user in this organisation?
        if (!await queryHandler.IsUserInOrganisation(request.UserId, organisationId))
        {
            return ValidationProblem($"User {request.UserId} is not in organisation {organisationId}");
        }

        // Can this user be activated?

        // Suspend this user from this organisation
        await queryHandler.ActivateUserOnOrganisation(request.UserId, organisationId);

        // TODO Add account log entry

        return Ok();
    }
}