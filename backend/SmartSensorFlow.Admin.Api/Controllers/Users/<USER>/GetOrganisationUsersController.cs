using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Users.GetOrganisationUsers.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Users.GetOrganisationUsers;

[Route("api/user")]
[ApiController]
public class GetOrganisationUsersController(IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpGet("organisation/{organisationId}")]
    [ProducesResponseType(typeof(GetUsersResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Get(int organisationId)
    {
        // TODO: Check invites
        var userTask = queryHandler.GetOrganisationUsers(organisationId);
        var ownerTask = queryHandler.GetOrganisationOwner(organisationId);

        await Task.WhenAll();

        var users = userTask.Result;
        users.Add(ownerTask.Result);

        return Ok(users.ToResponse());
    }
}