using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Users.SuspendUser.Models;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Users.SuspendUser;

[Route("api/user")]
[ApiController]
public class SuspendUserController(IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpPost("organisation/{organisationId}/suspend")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Suspend(int organisationId, SuspendUserRequest request)
    {
        // Is this user in this organisation?
        if (!await queryHandler.IsUserInOrganisation(request.UserId, organisationId))
        {
            return ValidationProblem($"User {request.UserId} is not in organisation {organisationId}");
        }

        // Can this user be suspended?

        // Suspend this user from this organisation
        await queryHandler.SuspendUserFromOrganisation(request.UserId, organisationId);

        // TODO Add account log entry

        return Ok();
    }
}