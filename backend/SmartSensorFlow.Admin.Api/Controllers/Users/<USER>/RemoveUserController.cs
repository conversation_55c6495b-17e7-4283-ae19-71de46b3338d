using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Users.RemoveUser.Models;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Users.RemoveUser;

[Route("api/user")]
[ApiController]
public class RemoveUserController(IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpPost("organisation/{organisationId}/remove")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> Remove(int organisationId, RemoveUserRequest request)
    {
        // Is this user in this organisation?
        if (!await queryHandler.IsUserInOrganisation(request.UserId, organisationId))
        {
            return ValidationProblem($"User {request.UserId} is not in organisation {organisationId}");
        }

        // Can this user be removed?

        // Remove this user from this organisation
        await queryHandler.RemoveUserFromOrganisation(request.UserId, organisationId);

        // TODO Add account log entry

        return Ok();
    }
}