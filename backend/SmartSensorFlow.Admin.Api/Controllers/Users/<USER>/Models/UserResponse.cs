using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Users.GetOrganisationUsers.Models;

public class UserResponse
{
    public required string Id { get; set; }

    public required string FirstName { get; set; }

    public required string LastName { get; set; }

    public required string EmailAddress { get; set; }

    public required AccountStatus UserStatus { get; set; }

    public required AccountStatus OrganisationStatus { get; set; }

    public required bool IsOwner { get; set; }
}
