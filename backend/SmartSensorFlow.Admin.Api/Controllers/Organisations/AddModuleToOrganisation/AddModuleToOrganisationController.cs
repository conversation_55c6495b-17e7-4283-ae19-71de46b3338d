using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Organisations.AddModuleToOrganisation.Models;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Organisations.AddModuleToOrganisation;

[Route("api/organisation")]
[ApiController]
public class AddModuleToOrganisationController(IQueryHandler queryHandler) : ProblemDetailsControllerBase
{
    [HttpPost("modules/add")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Add(AddModuleToOrganisationRequest request)
    {
        if (await queryHandler.IsOrganisationSubscribedToModule(request.OrganisationId, request.ModuleId))
        {
            return ValidationProblem($"Module {request.ModuleId} is already linked to organisation {request.OrganisationId}", []);
        }

        await queryHandler.LinkModuleToOrganisation(request.ModuleId, request.OrganisationId);
        return Ok();
    }
}
