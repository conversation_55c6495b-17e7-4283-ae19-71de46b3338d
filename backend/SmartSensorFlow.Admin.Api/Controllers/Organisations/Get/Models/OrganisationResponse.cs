using SmartSensorFlow.Admin.Api.Controllers.Modules.GetModules.Models;
using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Api.Controllers.Organisations.Get.Models;

public class OrganisationResponse
{
    public required int Id { get; set; }

    public required string Name { get; set; }

    public required bool IsOwner { get; set; }

    public required OrganisationState OrganisationStatus { get; set; }

    public required AccountStatus UserStatus { get; set; }

    public required DateTimeOffset CreatedAt { get; set; }
    
    public required List<ModulesResponse> Modules { get; set; }
}
