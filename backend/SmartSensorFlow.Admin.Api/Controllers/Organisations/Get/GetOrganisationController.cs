using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Organisations.Get.Models;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Api.Extensions;

namespace SmartSensorFlow.Admin.Api.Controllers.Organisations.Get;

[Route("api/organisation")]
[ApiController]
public class GetOrganisationController(IQueryHandler queryHandler, IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(GetOrganisationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Get()
    {
        // TODO: This call must be drastically improved, preferable as one query or a stored proc
        var userId = requestContextProvider.UserId;
        if (userId is null)
        {
            return Unauthorized();
        }
        var organisationsAsOwner = await queryHandler.GetByOwner(userId.Value);

        var organisationResponses = new List<OrganisationResponse>();
        foreach (var organisationAsOwner in organisationsAsOwner)
        {
            // Get modules for this org
            var moduleDataModels = await queryHandler.GetModulesByOrganisationID(organisationAsOwner.Id);
            var organisationResponse = new OrganisationResponse()
            {
                IsOwner = true,
                CreatedAt = organisationAsOwner.CreatedAt,
                Id = organisationAsOwner.Id,
                OrganisationStatus = (OrganisationState)organisationAsOwner.Status,
                UserStatus = AccountStatus.Active,
                Name = organisationAsOwner.Name,
                Modules = [.. moduleDataModels.Select(x => x.ToResponse())]
            };
            organisationResponses.Add(organisationResponse);
        }

        var organisationsAsUser = await queryHandler.GetUserOrganisations(userId.Value);
        foreach (var organisationAsUser in organisationsAsUser)
        {
            // Get modules for this org
            var moduleDataModels = await queryHandler.GetModulesByOrganisationID(organisationAsUser.Id);
            var organisationResponse = new OrganisationResponse()
            {
                IsOwner = false,
                CreatedAt = organisationAsUser.CreatedAt,
                Id = organisationAsUser.Id,
                OrganisationStatus = (OrganisationState)organisationAsUser.Status,
                UserStatus = organisationAsUser.UserStatus,
                Name = organisationAsUser.Name,
                Modules = [.. moduleDataModels.Select(x => x.ToResponse())]
            };
            organisationResponses.Add(organisationResponse);
        }

        var result = new GetOrganisationResponse()
        {
            Organisations = organisationResponses
        };
        return Ok(result);
    }
}
