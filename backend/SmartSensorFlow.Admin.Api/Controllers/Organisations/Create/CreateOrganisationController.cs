using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Organisations.Create.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Organisations.Create;

[Route("api/organisation")]
[ApiController]
public class CreateOrganisationController(IQueryHandler queryHandler, IRequestContextProvider requestContextProvider) : ControllerBase
{
    [HttpPost("create")]
    [RequiresAuthorization]
    [ProducesResponseType(typeof(CreateOrganisationResponse),StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Create(CreateOrganisationRequest request)
    {
        var userId = requestContextProvider.UserId;
        if (userId is null)
        {
            return Unauthorized();
        }
        var insertedId = await queryHandler.CreateOrganisation(userId.Value, request.Name);
        var response = new CreateOrganisationResponse()
        {
            OrganisationId = insertedId,
        };
        return Ok(response);
    }
}
