using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Controllers.Root.Response;

namespace SmartSensorFlow.Admin.Api.Controllers.Root;

[Route("/")]
[ApiController]
public class RootController : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(RootResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    public IActionResult Get()
    {
        return Ok(new RootResponse() { Message = "SmartSensorFlow - Admin API" });
    }
}