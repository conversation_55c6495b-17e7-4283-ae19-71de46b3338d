using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscription.Models;
using SmartSensorFlow.Admin.Api.Extensions;
using SmartSensorFlow.Admin.Data.DataServices;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscriptions;

[Route("api/billing")]
[ApiController]
public class GetSubscriptionsController(IQueryHandler queryHandler) : ControllerBase
{
    [HttpGet("{organisationId}/subscription")]
    [ProducesResponseType(typeof(GetSubscriptionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Get(int organisationId)
    {
        var subscriptions = await queryHandler.GetSubscriptionByOrganisationId(organisationId);
        var response = subscriptions.ToResponse();
        return Ok(response);
    }
}
