using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscriptionManagementLink.Models;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Subscription.GetSubscriptionManagementLink;

[Route("api/billing")]
[ApiController]
public class GetSubscriptionManagementLinkController(
    IQueryHandler queryHandler,
    IPaymentServiceResolver paymentServiceResolver) : ProblemDetailsControllerBase
{
    [HttpGet("{organisationId}/subscription/{subscriptionId}/manage")]
    [ProducesResponseType(typeof(GetSubscriptionManagementLinkResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [RequiresAuthorization]
    public async Task<IActionResult> Get(int organisationId, int subscriptionId)
    {
        var subscription = await queryHandler.GetSubscriptionBySubscriptionId(subscriptionId);

        // Sub must be valid and must in org requested
        if (subscription is null || subscription.OrganisationId != organisationId)
        {
            return ValidationProblem($"Invalid subscription id provided {subscriptionId}");
        }

        // Now we can generate link
        var secureLink = await paymentServiceResolver[subscription.ProductType].GenerateSubscriptionManagementLink(subscription.PaystackSubscriptionId);

        if (secureLink is null)
        {
            return ValidationProblem("Failed to generate secure link for account management", []);
        }
        return Ok(new GetSubscriptionManagementLinkResponse() { SecureLink = secureLink });
    }
}