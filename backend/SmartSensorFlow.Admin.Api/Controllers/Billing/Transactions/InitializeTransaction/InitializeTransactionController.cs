using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Api.Controllers.Billing.InitializeTransaction.Models;
using SmartSensorFlow.Admin.Api.Controllers.Billing.Transactions.InitializeTransaction.Models;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Transactions.InitializeTransaction;

[Route("api/billing/transaction")]
[ApiController]
public class InitializeTransactionController(
    IPaymentServiceResolver paymentServiceResolver,
    IQueryHandler queryHandler,
    IRequestContextProvider requestContextProvider) : ProblemDetailsControllerBase
{
    [HttpPost("initialize")]
    [ProducesResponseType(typeof(InitializeTransactionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    // TODO Permission check this including that the user is in this ORG
    public async Task<IActionResult> InitializeTransaction([FromBody] InitializeTransactionRequest request)
    {
        // Validate the user making this require
        var user = await queryHandler.GetUserByEmail(requestContextProvider.EmailAddress!);

        if (user is null)
        {
            return Unauthorized();
        }
        
        // Verify product exists
        Console.WriteLine($"Looking for product with id: {request.ProductId}");
        var product = await queryHandler.GetProductById(request.ProductId);
        if (product == null)
        {
            Console.WriteLine($"Product not found for plan code: {request.ProductId}");
            return ValidationProblem("Invalid product plan code", []);
        }
        Console.WriteLine($"Platform payment: Found product: {product.Name}");

        // Initialize transaction to get paystack URL
        var result = await paymentServiceResolver[product.Type].GeneratePaymentLink(
            "<EMAIL>",
            product.Currency,
            product.PlanId,
            user.Id,
            request.OrganizationId,
            product.Id
        );

        return Ok(new InitializeTransactionResponse{AuthorizationUrl = result});
    }
}