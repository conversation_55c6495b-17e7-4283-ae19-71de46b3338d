using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Transactions.VerifyTransaction;

public abstract class VerifyTransactionControllerBase(IPaymentServiceResolver paymentServiceResolver) : ProblemDetailsControllerBase
{
    protected abstract ProductType ProductType { get; }

    [NonAction]
    public async Task<IActionResult> VerifyTransaction(string reference)
    {
        var result = await paymentServiceResolver[ProductType].VerifyPayment(reference);
        return Ok(result);
    }
}