using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Transactions.VerifyTransaction;

[Route("api/billing/transaction")]
[ApiController]
public class VerifySupportTransactionController(IPaymentServiceResolver paymentServiceResolver) 
    : VerifyTransactionControllerBase(paymentServiceResolver)
{
    protected override ProductType ProductType => ProductType.Support;

    [HttpGet("verify/support")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> VerifySupportTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        return await VerifyTransaction(reference);
    }
}
