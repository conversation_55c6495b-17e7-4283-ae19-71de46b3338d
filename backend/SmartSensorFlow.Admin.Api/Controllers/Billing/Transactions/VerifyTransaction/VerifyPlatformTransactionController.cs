using Microsoft.AspNetCore.Mvc;
using SmartSensorFlow.Admin.Api.Attributes;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Controllers.Billing.Transactions.VerifyTransaction;

[Route("api/billing/transaction")]
[ApiController]
public class VerifyPlatformTransactionController(IPaymentServiceResolver paymentServiceResolver) 
    : VerifyTransactionControllerBase(paymentServiceResolver)
{
    protected override ProductType ProductType => ProductType.Platform;

    [HttpGet("verify/platform")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [RequiresAuthorization]
    public async Task<IActionResult> VerifyPlatformTransaction([FromQuery] string reference)
    {
        if (string.IsNullOrEmpty(reference))
        {
            return ValidationProblem("Reference is required", []);
        }

        return await VerifyTransaction(reference);
    }
}