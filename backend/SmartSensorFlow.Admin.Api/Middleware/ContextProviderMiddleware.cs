﻿using SmartSensorFlow.Admin.Common.Constants;
using SmartSensorFlow.Admin.Common.Middleware;
using SmartSensorFlow.Admin.Security.Token;

namespace SmartSensorFlow.Admin.Api.Middleware;

public class ContextProviderMiddleware(RequestDelegate next)
{
    public async Task Invoke(HttpContext context, ITokenService tokenService, IRequestContextProvider requestContextProvider)
    {
        var token = context.Request.Cookies[CookieConstants.AuthTokenCookieName];
        var claimsResult = await tokenService.ValidateJWTToken(token);
        if (claimsResult != null)
        {
            requestContextProvider.Init(claimsResult.UserId, claimsResult.Email);
        }
        await next(context);
    }
}
