﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using SmartSensorFlow.Admin.Common.Middleware;

namespace SmartSensorFlow.Admin.Api.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class RequiresAuthorizationAttribute : Attribute, IAuthorizationFilter
{
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        // If userId is not set by middleware, then we do not have a token or the token was invalid
        var requestContextProvider = context.HttpContext.RequestServices.GetService<IRequestContextProvider>() ?? throw new Exception("IRequestContextProvider must be registered in the service collection");
        if (string.IsNullOrEmpty(requestContextProvider.EmailAddress) ||  requestContextProvider.UserId is null)
        {
            // not logged in or role not authorized
            context.Result = new UnauthorizedResult();
        }
    }
}
