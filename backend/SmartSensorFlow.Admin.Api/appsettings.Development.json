{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Jwt": {"Secret": "********************************at-least-256-bytes-long"}, "Api": {"BasePath": "api.admin.local.ssf", "IdentityInviteLink": "api.auth.local.ssf/invite?inviteLink={0}"}, "Cors": {"AllowedOrigins": "https://auth.local.ssf:8443,https://admin.local.ssf:8443"}, "SQL": {"ConnectionString": "Host=aws-0-eu-west-1.pooler.supabase.com;Port=5432;Database=postgres;Username=postgres.rynxgscvnodfnmflpfwp;Password=****************;"}, "PaystackPlatform": {"SecretKey": "sk_test_d6d97390467f3280323931ddf92939ab392a2580", "PublicKey": "pk_test_d19530e7c057ec4eb7142ce000fc198d8349be56", "CallbackUrlBase": "https://admin.local.ssf:8443/payment-success?type=platform"}, "PaystackSupport": {"SecretKey": "sk_test_2a9c19ec1923dfbc2520c8dcc02314a0d49dc078", "PublicKey": "pk_test_9d1412bea51827824feddd2ad0301819cb567368", "CallbackUrlBase": "https://admin.local.ssf:8443/payment-success?type=support"}, "Email": {"ServiceId": "service_3c5oqdd", "PublicKey": "iErF9HvSeAFijSLLn", "PrivateKey": "t2Czz1vagJsdrC5ESmPee"}, "InviteURLs": {"Admin": "https://admin.local.ssf:8443/", "Identity": "https://auth.local.ssf:8443/invite?inviteLink={0}"}}