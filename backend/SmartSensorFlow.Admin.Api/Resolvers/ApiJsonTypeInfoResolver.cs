using System.Text.Json.Serialization.Metadata;
using SmartSensorFlow.Admin.Api.Controllers.Products.Models;

namespace SmartSensorFlow.Admin.Api.Resolvers;

public static class ApiJsonTypeInfoResolver
{
  public static DefaultJsonTypeInfoResolver ApiResolver { get; } =
      new DefaultJsonTypeInfoResolver
      {
        Modifiers =
        {
          static typeInfo =>
          {
            if (typeInfo.Type == typeof(ProductAttributesBase))
            {
              typeInfo.PolymorphismOptions = new()
              {
                DerivedTypes =
                {
                  new JsonDerivedType(typeof(ProductAttributesPlatform), nameof(ProductAttributesPlatform)),
                  new JsonDerivedType(typeof(ProductAttributesSupport), nameof(ProductAttributesSupport))
                }
              };
            }
          }
      }
      };
}