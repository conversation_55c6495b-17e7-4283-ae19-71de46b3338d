using System.Diagnostics.CodeAnalysis;
using System.IO.Compression;
using Ardalis.GuardClauses;
using Microsoft.AspNetCore.ResponseCompression;

namespace SmartSensorFlow.Admin.Api.Extensions;

// <summary>
/// Extension methods for <see cref="IServiceCollection"/> and response compression.
/// <see href="https://learn.microsoft.com/en-us/aspnet/core/performance/response-compression?view=aspnetcore-7.0">Response Compression</see>
/// </summary>
[ExcludeFromCodeCoverage]
public static class ResponseCompressionServiceExtensions
{
    public static void AddCustomResponseCompression(this IServiceCollection services)
    {
        Guard.Against.Null(services);

        services.AddResponseCompression(options =>
        {
            options.EnableForHttps = true;
            options.Providers.Add<BrotliCompressionProvider>();
            options.Providers.Add<GzipCompressionProvider>();
        });

        services.Configure<BrotliCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.Fastest;
        });

        services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.SmallestSize;
        });
    }
}

