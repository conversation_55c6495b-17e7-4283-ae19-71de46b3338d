using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Integration.Paddle;
using SmartSensorFlow.Admin.Integration.Paddle.Config;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class PaddleServiceExtension
{
    public static void AddPaddlePaymentService(this IServiceCollection services, IConfiguration configuration)
    {
        var paddleConfiguration = configuration.GetSection(PaddleConfiguration.SectionName).Get<PaddleConfiguration>();
        Guard.Against.Null(paddleConfiguration);
        Guard.Against.Null(paddleConfiguration.PaddleURL);
        Guard.Against.Null(paddleConfiguration.PaddleSecretKey);

        services.AddSingleton(paddleConfiguration);
        services.AddSingleton<IPaddleService, PaddleService>();
    }
}
