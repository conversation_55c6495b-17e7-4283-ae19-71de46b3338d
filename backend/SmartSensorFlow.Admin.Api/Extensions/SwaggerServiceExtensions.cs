using System.Diagnostics.CodeAnalysis;
using Microsoft.OpenApi.Models;
using SmartSensorFlow.Admin.Api;

namespace SmartSensorFlow.Admin.Api.Extensions;

// <summary>
/// Extension methods for <see cref="IServiceCollection"/> to add custom swagger configuration
/// </summary>
[ExcludeFromCodeCoverage]
public static class SwaggerServiceExtensions
{
    public static void AddCustomSwagger(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo{ Title = "SSF Identity Server API", Version = "v1" });
            // Group actions by their namespace parts after the "<asssemblyname>.Controllers" part of their namespace,
            // up to a maximum of one namespace parts. This grouping can be enhanced over time.
            var skipNamespaceParts = typeof(ApiAssemblyType).Namespace!.Split(Type.Delimiter).Length + 1;
            c.TagActionsBy(description => [string.Join(" ", description.ActionDescriptor.DisplayName!.Split(' ')[0].Split(Type.Delimiter).Skip(skipNamespaceParts).Take(1))]);

            c.AddSecurityRequirement(new OpenApiSecurityRequirement()
            {
                {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    },
                    Scheme = "oauth2",
                    Name = "Bearer",
                    In = ParameterLocation.Header,

                    },
                    new List<string>()
                }
            });
        });
    }
}
