using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Data.DataServices.Services;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class ServiceExtensions
{
    public static void AddCustomServices(this IServiceCollection services, IConfiguration configuration)
    {
        var sqlConfiguration = configuration.GetSection(SQLConfiguration.SectionName).Get<SQLConfiguration>();
        Guard.Against.Null(sqlConfiguration);
        Guard.Against.Null(sqlConfiguration.ConnectionString);

        services.AddSingleton(sqlConfiguration);
        services.AddScoped<IQueryHandler, QueryHandler>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IPermissionService, PermissionService>();
    }
}
