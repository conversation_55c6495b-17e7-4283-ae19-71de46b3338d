using System.Diagnostics.CodeAnalysis;
using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Api.Configuration;

namespace SmartSensorFlow.Admin.Api.Extensions;

/// <summary>
/// Extension methods for <see cref="IServiceCollection"/> to add custom Cross-origin resource sharing (CORS).
/// </summary>
[ExcludeFromCodeCoverage]
internal static class CorsServiceExtensions
{
    private const string CorsPolicy = "DefaultCorsPolicy";

    /// <summary>
    /// Add cross-origin resource sharing (CORS) services.
    /// </summary>
    /// <param name="services">Service collection.</param>
    /// <param name="configuration">Configuration.</param>
    public static void AddCustomCors(this IServiceCollection services, IConfiguration configuration)
    {
        var corsConfiguration = configuration.GetSection(CorsConfiguration.SectionName).Get<CorsConfiguration>();

        Guard.Against.Null(corsConfiguration);
        Guard.Against.Null(corsConfiguration.AllowedOrigins);

        services.AddCors(corsOptions =>
            corsOptions.AddPolicy(CorsPolicy, corsPolicyBuilder =>
            {
                var allowedOrigins = corsConfiguration.AllowedOrigins!.Split(',', StringSplitOptions.RemoveEmptyEntries);

                corsPolicyBuilder
                    .WithOrigins(allowedOrigins)
                    .AllowAnyMethod()
                    .AllowAnyHeader()
                    .AllowCredentials();
            }));
    }

    /// <summary>
    /// Add cross-origin resource sharing (CORS) middleware to the web application pipeline to allow cross domain requests.
    /// </summary>
    /// <param name="application">Application.</param>
    public static void UseCustomCors(this IApplicationBuilder application)
    {
        Guard.Against.Null(application);
        application.UseCors(CorsPolicy);
    }
}

