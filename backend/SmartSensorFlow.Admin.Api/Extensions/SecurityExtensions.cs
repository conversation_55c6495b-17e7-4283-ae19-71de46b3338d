using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Security.Token;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class SecurityExtensions
{
    public static void AddCustomSecurity(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtConfiguration = configuration.GetSection(JwtConfiguration.SectionName).Get<JwtConfiguration>();
        Guard.Against.Null(jwtConfiguration);
        Guard.Against.Null(jwtConfiguration.Secret);
        var apiConfiguration = configuration.GetSection(ApiConfiguration.SectionName).Get<ApiConfiguration>();
        Guard.Against.Null(apiConfiguration);
        Guard.Against.Null(apiConfiguration.BasePath);

        services.AddSingleton(apiConfiguration);
        services.AddSingleton(jwtConfiguration);
        services.AddSingleton<ITokenService, TokenService>();
    }
}
