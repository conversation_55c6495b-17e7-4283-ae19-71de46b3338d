using System.Diagnostics.CodeAnalysis;
using Ardalis.GuardClauses;
//using Hellang.Middleware.ProblemDetails;

namespace SmartSensorFlow.Admin.Api.Extensions;

/// <summary>
/// Problem details is a fantastic, industry standard way to manage errors
/// RFC : https://tools.ietf.org/html/rfc7807
/// Microsoft : https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors?view=aspnetcore-7.0
/// Middleware : https://github.com/khellang/Middleware
/// </summary>
[ExcludeFromCodeCoverage]
public static class ProblemDetailsServiceExtensions
{
    /// <summary>
    /// Configure problem details
    /// </summary>
    public static void AddCustomProblemDetails(this IServiceCollection services)
    {
        Guard.Against.Null(services);

        services.AddProblemDetails(options =>
        {
            // Ensure ProblemDetails responses are returned as JSON
            options.CustomizeProblemDetails = (context) =>
            {
                context.HttpContext.Response.ContentType = "application/json";
            };
        });
    }

    /// <summary>
    /// Map various exceptions to a specified HTTP status code with the relevant problem details
    /// </summary>
    /// <param name="options"></param>
    // private static void ProblemDetails(Hellang.Middleware.ProblemDetails.ProblemDetailsOptions options)
    // {
    //     // HTTP 400 (Generally used for validation)
    //     options.MapToStatusCode<ArgumentNullException>(StatusCodes.Status400BadRequest);
    //     options.MapToStatusCode<InvalidOperationException>(StatusCodes.Status400BadRequest);
    //     options.MapToStatusCode<ValidationException>(StatusCodes.Status400BadRequest);

    //     // HTTP 401
    //     options.MapToStatusCode<AuthenticationException>(StatusCodes.Status401Unauthorized);

    //     // This will map HttpRequestException to the 503 Service Unavailable status code.
    //     options.MapToStatusCode<HttpRequestException>(StatusCodes.Status503ServiceUnavailable);

    //     // Catch-All Exception (needs to  be added last)
    //     options.MapToStatusCode<Exception>(StatusCodes.Status500InternalServerError);
    // }

}
