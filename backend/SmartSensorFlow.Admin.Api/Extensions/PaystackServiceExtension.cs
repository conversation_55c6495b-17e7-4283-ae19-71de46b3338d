using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Integration.Paystack.Config;
using SmartSensorFlow.Admin.IOC;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class PaystackServiceExtension
{
    public static void AddPaystackPaymentService(this IServiceCollection services, IConfiguration configuration)
    {
        var platformConfiguration = configuration.GetSection(PaystackPlatformConfiguration.SectionName).Get<PaystackPlatformConfiguration>();
        var supportConfiguration = configuration.GetSection(PaystackSupportConfiguration.SectionName).Get<PaystackSupportConfiguration>();

        Guard.Against.Null(platformConfiguration);
        Guard.Against.Null(supportConfiguration);
        Guard.Against.Null(platformConfiguration.SecretKey);
        Guard.Against.Null(supportConfiguration.SecretKey);

        services.AddSingleton(platformConfiguration);
        services.AddSingleton(supportConfiguration);
        services.AddSingleton<IPaymentServiceResolver, PaymentServiceResolver>();
    }
}
