using System.Text.Json;
using SmartSensorFlow.Admin.Api.Controllers.Products.Models;
using SmartSensorFlow.Admin.Common.Constants;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Api.Extensions.Responses;

public static class ProductResponseExtensions
{
    public static GetProductsResponse ToResponse(this List<ProductDataModel> data)
    {
        var response = new GetProductsResponse()
        {
            Products = [.. data.Select(ToResponse)]
        };
        return response;
    }

    public static ProductResponse ToResponse(this ProductDataModel data)
    {
        var response = new ProductResponse()
        {
            Id = data.Id,
            PlanID = data.PlanId,
            Attributes = GetProductAttributes(data.Type, data.Attributes),
            Description = data.Description,
            Type = data.Type,
            Name = data.Name,
            Price = BuildCurrencyString(data.Price, data.Currency, data.BillingCycle)
        };
        return response;
    }

    private static ProductAttributesBase GetProductAttributes(ProductType type, string json)
    {
        return type switch
        {
            ProductType.Platform => JsonSerializer.Deserialize<ProductAttributesPlatform>(json, CustomJsonSerializerOptions.jsonSerializerOptions) ?? throw new Exception("Invalid attribute JSON for PLATFORM product TYPE"),
            ProductType.Support => JsonSerializer.Deserialize<ProductAttributesSupport>(json, CustomJsonSerializerOptions.jsonSerializerOptions) ?? throw new Exception("Invalid attribute JSON from SUPPORT product TYPE"),
            _ => throw new Exception("Unknown product type")
        };
    }

    private static string BuildCurrencyString(decimal price, string currencyCode, BillingCycle billingCycle)
    {
        return $"{TranslateCurrencyCode(currencyCode)}{price}/{billingCycle}";
    }

    private static string TranslateCurrencyCode(string currencyCode)
    {
        return currencyCode switch
        {
            "USD" => "$",
            "ZAR" => "R",
            _ => "",
        };
    }
}