using System.Diagnostics.CodeAnalysis;
using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Api.Configuration;

namespace SmartSensorFlow.Admin.Api.Extensions;

internal static class InviteExtensions
{
    public static void AddInviteConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        var inviteConfig = configuration.GetSection(InviteConfiguration.SectionName).Get<InviteConfiguration>();

        Guard.Against.Null(inviteConfig);
        services.AddSingleton(inviteConfig);
    }
}