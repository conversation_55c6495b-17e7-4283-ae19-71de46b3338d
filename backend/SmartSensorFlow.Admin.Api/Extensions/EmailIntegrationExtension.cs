using Ardalis.GuardClauses;
using SmartSensorFlow.Admin.Integration.EmailJS;
using SmartSensorFlow.Admin.Integration.EmailJS.Configuration;

namespace SmartSensorFlow.Admin.Api.Extensions;

public static class EmailIntegrationExtension
{
    public static void AddEmailIntegration(this IServiceCollection services, IConfiguration configuration)
    {
        var emailConfiguration = configuration.GetSection(EmailConfiguration.SectionName).Get<EmailConfiguration>();
        Guard.Against.Null(emailConfiguration);
        services.AddSingleton(emailConfiguration);

        services.AddSingleton<IEmailService, EmailService>();
    }
}
