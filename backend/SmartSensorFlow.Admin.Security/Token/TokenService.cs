﻿using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Security.Token.Results;

namespace SmartSensorFlow.Admin.Security.Token;

public class TokenService(JwtConfiguration config, ApiConfiguration apiConfiguration) : ITokenService
{
    public string GenerateJWTToken(int userId, string email)
    {
        var tokenHandler = new JsonWebTokenHandler();
        var key = Encoding.UTF8.GetBytes(config.Secret);
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Email, email)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddDays(7),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
            Issuer = apiConfiguration.BasePath,
            Audience = apiConfiguration.BasePath
        };

        // NOTE: Figure out how to sign these things
        // NOTE: This should be signed - but our key sucks - so fix it
        return tokenHandler.CreateToken(tokenDescriptor);
    }

    public async Task<TokenClaimsResult?> ValidateJWTToken(string? token)
    {
        if (token == null)
            return null;

        var tokenHandler = new JsonWebTokenHandler();
        var key = Encoding.ASCII.GetBytes(config.Secret);

        var tokenValidationResult = await tokenHandler.ValidateTokenAsync(token, new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(key),
            ValidateIssuer = false,
            ValidateAudience = false,
            // set clockskew to zero so tokens expire exactly at token expiration time (instead of 5 minutes later)
            ClockSkew = TimeSpan.Zero
        });

        // TODO: Extend this to check auth problems properly
        if (tokenValidationResult.IsValid != true)
            return null;

        int userId = -1;
        string emailAddress = string.Empty;

        // ============= Required Claims
        // 1. User ID
        if (tokenValidationResult.Claims.TryGetValue(ClaimTypes.NameIdentifier, out var userIdClaim))
        {
            userId = int.Parse((string)userIdClaim);
        }

        // 2. Email address
        if (tokenValidationResult.Claims.TryGetValue(ClaimTypes.Email, out var emailClaim))
        {
            emailAddress = (string)emailClaim;
        }

        if (userId == -1 || string.IsNullOrEmpty(emailAddress))
        {
            // Invalid claim
            return null;
        }

        return new TokenClaimsResult(userId, emailAddress);
    }
}