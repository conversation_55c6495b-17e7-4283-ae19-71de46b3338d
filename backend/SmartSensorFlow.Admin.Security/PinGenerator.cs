﻿using System.Security.Cryptography;

namespace SmartSensorFlow.Admin.Security;

public static class PinGenerator
{
    private const string useableNumbers = "1234567890";
    private const string useableCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    public static string GenerateNumeric(int length = 4)
    {
        return Generate(length, useableNumbers);
    }

    // TODO: replace this as it can generate duplicated - very small chance but it can
    public static string GenerateAlphabetical(int length = 32)
    {
        return Generate(length, useableCharacters);
    }

    private static string Generate(int length, string characters)
    {
        var result = "";
        for (int i = 0; i < length; i++)
        {
            var rand = RandomNumberGenerator.GetInt32(characters.Length);
            result += characters[rand];
        }
        return result;
    }
}
