﻿using System.Text.Json.Serialization;

namespace SmartSensorFlow.Admin.Integration.EmailJS.Models;

public class EmailPayload
{
    [JsonPropertyName("service_id")]
    public required string ServiceId { get; set; }

    [JsonPropertyName("template_id")]
    public required string TemplateId { get; set; }

    [JsonPropertyName("user_id")]
    public required string PublicKey { get; set; }

    [JsonPropertyName("accessToken")]
    public required string PrivateKey { get; set; }

    [JsonPropertyName("template_params")]
    public required Dictionary<string, string> TemplateParams { get; set; }
}
