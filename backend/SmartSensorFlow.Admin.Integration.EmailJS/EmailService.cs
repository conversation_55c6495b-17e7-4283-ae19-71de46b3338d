﻿using System.Net.Http.Json;
using SmartSensorFlow.Admin.Integration.EmailJS.Configuration;
using SmartSensorFlow.Admin.Integration.EmailJS.Models;

namespace SmartSensorFlow.Admin.Integration.EmailJS;

public class EmailService(EmailConfiguration emailConfig) : IEmailService
{
    private readonly HttpClient httpClient = new();

    public async Task SendInviteEmail(string name, string surname, string organisationName, string emailAddress, string inviteLink)
    {
        var data = new Dictionary<string, string>()
        {
            { "emailAddress", emailAddress },
            { "organization", organisationName },
            { "name", name },
            { "surname", surname },
            { "organizationLink", inviteLink },
        };
        await SendEmail("template_bihiev9", data);
    }

    private async Task SendEmail(string templateId, Dictionary<string, string> data)
    {
        var model = new EmailPayload()
        {
            PublicKey = emailConfig.PublicKey,
            ServiceId = emailConfig.ServiceId,
            PrivateKey = emailConfig.PrivateKey,
            TemplateId = templateId,
            TemplateParams = data
        };

        var result = await httpClient.PostAsJsonAsync("https://api.emailjs.com/api/v1.0/email/send", model);

        // TODO: Communication log
    }
}
