using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json.Linq;
using SmartSensorFlow.Admin.Integration.Paddle.Config;
using SmartSensorFlow.Admin.Integration.Paddle.Enums;
using SmartSensorFlow.Admin.Integration.Paddle.Models;
using Microsoft.Extensions.Logging;

namespace SmartSensorFlow.Admin.Integration.Paddle;

public class PaddleService : IPaddleService
{
    private readonly HttpClient _httpClient;
    private readonly PaddleConfiguration _configuration;
    private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    private readonly ILogger<PaddleService> _logger;

    public PaddleService(PaddleConfiguration configuration, ILogger<PaddleService> logger)
    {
        _configuration = configuration;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _configuration.PaddleSecretKey);
        _httpClient.BaseAddress = new Uri(_configuration.PaddleURL);
        _logger = logger;
    }

    public async Task CancelSubscription(string paddleSubscriptionId, CancellationType cancelType)
    {
        var data = new CancelSubscriptionModel()
        {
            EffectiveFrom = cancelType == CancellationType.Immediate ? "immediately" : "next_billing_period"
        };
        var jsonData = JsonSerializer.Serialize(data, _jsonSerializerOptions);
        var httpContent = new StringContent(jsonData, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync($"subscriptions/{paddleSubscriptionId}/cancel", httpContent);

        var content = await response.Content.ReadAsStringAsync();
        var responseData = JObject.Parse(content);
        _logger.LogInformation("Cancel subscription response data: {response}", responseData);
    }
}