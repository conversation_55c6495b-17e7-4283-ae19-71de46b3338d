using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Dto;

public class UserDto
{
    public required string Id { get; set; }

    public required string FirstName { get; set; }

    public required string LastName { get; set; }

    public required string EmailAddress { get; set; }

    public required AccountStatus UserStatus { get; set; }

    public required AccountStatus OrganisationStatus { get; set; }

    public required bool IsOwner { get; set; }
}
