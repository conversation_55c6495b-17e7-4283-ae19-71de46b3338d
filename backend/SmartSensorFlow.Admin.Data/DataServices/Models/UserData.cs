namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class UserData
{
    public required int Id { get; set; }

    public required string FirstName { get; set; }

    public required string LastName { get; set; }

    public required string EmailAddress { get; set; }

    public required string HashedPassword { get; set; }

    public required int Status { get; set; }

    public string? VerificationPin { get; set; }

    public DateTimeOffset? VerificationPinExpiry { get; set; }

    public string? PasswordResetToken { get; set; }

    public DateTimeOffset? PasswordResetTokenExpiry { get; set; }

    public string? RefreshToken { get; set; }

    public DateTimeOffset? RefreshTokenExpiry { get; set; }

    public required string CreatedBy { get; set; }

    public required DateTimeOffset CreatedDate { get; set; }

    public required string ModifiedBy { get; set; }

    public required DateTimeOffset ModifyDate { get; set; }
}
