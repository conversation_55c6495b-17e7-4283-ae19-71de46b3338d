using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class PermissionData
{
    public required int Id { get; set; }

    public required string PermissionName { get; set; }

    public required string PermissionDescription { get; set; }

    public required PermissionScope Scope { get; set; }
    
    public required PermissionDomain Domain { get; set; }
}