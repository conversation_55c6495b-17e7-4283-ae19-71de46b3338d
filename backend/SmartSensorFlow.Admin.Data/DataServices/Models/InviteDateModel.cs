using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class InviteDateModel
{
    public required int Id { get; set; }

    public required string Email { get; set; }

    public required string InviteKey { get; set; }

    public required int OrganisationId { get; set; }

    public required InviteType InviteType { get; set; }

    public required DateTimeOffset InviteExpireDate { get; set; }

    public required InviteStatus InviteStatus { get; set; }

    public string? RejectionReason { get; set; }

    public required string InviteMetadata { get; set; }

    public required int CreatedBy { get; set; }
    
    public required DateTimeOffset CreatedAt { get; set; }
}
