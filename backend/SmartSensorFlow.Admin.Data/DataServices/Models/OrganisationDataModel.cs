using SmartSensorFlow.Admin.Common.Enums;

namespace SmartSensorFlow.Admin.Data.DataServices.Models;

public class OrganisationDataModel
{
    public required int Id { get; set;}
    
    public required string Name { get; set; }
    
    public required int OwnerId { get; set; }
    
    public required AccountStatus Status { get; set; }

    public required AccountStatus UserStatus { get; set; }
    
    public required int CreatedBy { get; set; }
    
    public required DateTimeOffset CreatedAt { get; set; }
    
    public required int ModifiedBy { get; set; }
    
    public required DateTimeOffset ModifiedAt { get; set; }
}
