using Dapper;
using Npgsql;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Data.DataServices.Services;

public class UserService(SQLConfiguration config) : IUserService
{
    public async Task<UserData?> GetUserByEmail(string emailAddress)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Users\" WHERE \"EmailAddress\" = @emailAddress";
        var result = await connection.QueryAsync<UserData>(sql, new { emailAddress });
        return result.FirstOrDefault();
    }

    public async Task<UserData?> GetUserById(int userId)
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Users\" WHERE \"Id\" = @userId";
        var result = await connection.QueryAsync<UserData>(sql, new { userId });
        return result.FirstOrDefault();
    }
}
