using Dapper;
using Npgsql;
using SmartSensorFlow.Admin.Common.Configuration;
using SmartSensorFlow.Admin.Data.DataServices.Models;

namespace SmartSensorFlow.Admin.Data.DataServices.Services;

public class PermissionService(SQLConfiguration config) : IPermissionService
{
    public async Task<List<PermissionData>> GetAllPermissions()
    {
        using var connection = new NpgsqlConnection(config.ConnectionString);
        var sql = "SELECT * FROM \"Permissions\"";
        var permissions = await connection.QueryAsync<PermissionData>(sql);
        return [.. permissions];
    }
}