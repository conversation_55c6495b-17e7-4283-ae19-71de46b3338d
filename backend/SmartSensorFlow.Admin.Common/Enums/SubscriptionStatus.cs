namespace SmartSensorFlow.Admin.Common.Enums;

// These values mimic what paystack is sending us
public enum SubscriptionStatus
{
    Active, // The subscription is currently active, and will be charged on the next payment date.
    NonRenewing, // The subscription is currently active, but we won't be charging it on the next payment date. This occurs when a subscription is about to be complete, or has been cancelled (but we haven't reached the next payment date yet).
    Attention, // The subscription is still active, but there was an issue while trying to charge the customer's card. The issue can be an expired card, insufficient funds, etc. We'll attempt charging the card again on the next payment date.
    Completed, // The subscription is complete, and will no longer be charged.
    Cancelled // The subscription has been cancelled, and we'll no longer attempt to charge the card on the subscription.
}
