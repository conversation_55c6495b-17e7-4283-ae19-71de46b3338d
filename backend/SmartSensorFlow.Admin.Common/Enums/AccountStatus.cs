namespace SmartSensorFlow.Admin.Common.Enums;

public enum AccountStatus
{
    Pending, // New accounts, before verification/payments etc
    Active, // Accounts that can fully work on the system
    Suspended, // Accounts that are temporarily suspended from the platform or from an organisation
    Removed // Accounts that have been removed from the platform or organisation - TODO: How will we handle this? Delete the data?
}
