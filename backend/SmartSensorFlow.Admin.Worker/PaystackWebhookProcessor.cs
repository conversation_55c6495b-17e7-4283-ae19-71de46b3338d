using System.Threading.Channels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.IOC;
using SmartSensorFlow.Admin.Worker.Helpers;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Worker;

public class PaystackWebhookProcessor(
    Channel<PaystackWebhookMessage> queue,
    ILogger<PaystackWebhookProcessor> logger,
    IServiceProvider services) : IHostedService
{
    private Task? _workerTask;
    private CancellationTokenSource _cts = new();

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _workerTask = Task.Run(() => RunAsync(_cts.Token), cancellationToken);
        return Task.CompletedTask;
    }

    public async Task RunAsync(CancellationToken cancellationToken)
    {
        try
        {
            await foreach (var message in queue.Reader.ReadAllAsync(cancellationToken))
            {
                var responseData = JObject.Parse(message.RawBody);
                var webhookEvent = responseData["event"]?.ToString();

                if (webhookEvent is not null)
                {
                    switch (webhookEvent)
                    {
                        case "charge.success":
                            Console.WriteLine("============ CHARGE SUCCESS ==============");
                            Console.WriteLine(message.RawBody);
                            await ProcessChargeSuccess(responseData);
                            break;
                        case "subscription.create":
                            Console.WriteLine("============ SUBSCRIPTION CREATE ==============");
                            Console.WriteLine(message.RawBody);
                            await ProcessSubscriptionCreated(responseData, message.ProductType);
                            break;
                        // case "subscription.disable":
                        //     await ProcessSubscriptionDisabled(responseData);
                        //     break;
                        // case "invoice.create":
                        //     await ProcessInvoiceCreated(responseData);
                        //     break;
                        // case "invoice.payment_failed":
                        //     await ProcessInvoicePaymentFailed(responseData);
                        //     break;
                        default:
                            logger.LogInformation("Unhandled Paystack webhook event: {Event}", webhookEvent);
                            break;
                    }
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError("Exception thrown in Paystack webhook processor: {e}", e.Message);
        }
    }

    private async Task ProcessChargeSuccess(JObject data)
    {
        using var scope = services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();
        // TODO: Add status to models
        var status = data["data"]?["status"]?.ToString();

        if (status is null)
        {
            // Return? Not sure what to do now?
            Console.Error.WriteLine("No status in payload");
            return;
        }

        // Get metadata
        var orgId = data["data"]?["metadata"]?["org_id"]?.ToString().IntFromString();
        var productId = data["data"]?["metadata"]?["product_id"]?.ToString().IntFromString();
        var amount = data["data"]?["amount"]?.ToString().DecimalFromString() / 100;
        var paymentDateString = data["data"]?["paid_at"]?.ToString();
        var currency = data["data"]?["currency"]?.ToString();

        if (orgId is null ||
            productId is null ||
            amount is null ||
            paymentDateString is null ||
            currency is null)
        {
            // No payment date
            Console.Error.WriteLine("Incorrect data in payload");
            return;
        }

        await queryHandler.CreatePayment(productId.Value, orgId.Value, amount.Value, currency, DateTime.Parse(paymentDateString).ToUniversalTime());
    }

    private async Task ProcessSubscriptionCreated(JObject data, ProductType productType)
    {
        using var scope = services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();
        var paymentServiceResolver = scope.ServiceProvider.GetRequiredService<IPaymentServiceResolver>();
        var status = data["data"]?["status"]?.ToString();

        if (status is null)
        {
            // Return? Not sure what to do now?
            Console.Error.WriteLine("No status in payload");
            return;
        }

        // Get the required data to update this subscription
        var subscriptionCode = data["data"]?["subscription_code"]?.ToString();
        if (subscriptionCode is null)
        {
            // Return? Not sure what to do now?
            Console.Error.WriteLine("No 'subscription_code' in payload");
            return;
        }
        // Get this subscription's full details
        var subscription = await paymentServiceResolver[productType].GetSubscription(subscriptionCode);
        var responseData = JObject.Parse(subscription);
        var metadata = responseData["data"]?["invoices"]?[0]?["metadata"];

        if (metadata is null)
        {
            // Return? Not sure what to do now?
            Console.Error.WriteLine("No 'metadata' in invoices payload");
            return;
        }
        var userId = metadata["user_id"]?.ToString().IntFromString();
        var orgId = metadata["org_id"]?.ToString().IntFromString();
        var productId = metadata["product_id"]?.ToString().IntFromString();
        var nextPaymentDateStr = responseData["data"]?["next_payment_date"]?.ToString();

        if (userId is null ||
            orgId is null ||
            productId is null ||
            nextPaymentDateStr is null)
        {
            // Return? Not sure what to do now?
            Console.Error.WriteLine("Not all required metadata payload");
            return;
        }
        await queryHandler.CreateSubscription(productId.Value, orgId.Value, subscriptionCode, DateTime.Parse(nextPaymentDateStr).ToUniversalTime(), userId.Value);
    }

    // private async Task ProcessSubscriptionDisabled(JObject responseData)
    // {
    //     using var scope = services.CreateScope();
    //     var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();

    //     try
    //     {
    //         var subscriptionData = responseData["data"];
    //         var subscriptionCode = subscriptionData?["subscription_code"]?.ToString();

    //         if (string.IsNullOrEmpty(subscriptionCode))
    //         {
    //             logger.LogError("Missing subscription code in disable webhook");
    //             return;
    //         }

    //         // Update subscription status to cancelled
    //         await queryHandler.CancelPaystackSubscription(subscriptionCode, subscriptionType);

    //         logger.LogInformation("Successfully processed subscription disabled: {SubscriptionCode} for {SubscriptionType}",
    //             subscriptionCode, subscriptionType);
    //     }
    //     catch (Exception ex)
    //     {
    //         logger.LogError(ex, "Error processing subscription disabled webhook");
    //     }
    // }

    // private async Task ProcessInvoiceCreated(JObject responseData)
    // {
    //     logger.LogInformation("Invoice created for {SubscriptionType}: {Data}", subscriptionType, responseData);
    //     // Implement invoice processing logic if needed
    // }

    // private async Task ProcessInvoicePaymentFailed(JObject responseData)
    // {
    //     logger.LogWarning("Invoice payment failed for {SubscriptionType}: {Data}", subscriptionType, responseData);
    //     // Implement payment failure handling logic if needed
    // }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        return _workerTask ?? Task.CompletedTask;
    }

    // private static (int userId, int organisationId, int productId) GetMetadata(JObject metadata)
    // {

    // }
}
