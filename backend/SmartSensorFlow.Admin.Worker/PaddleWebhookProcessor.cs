using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Data.DataServices;
using SmartSensorFlow.Admin.Worker.Messages;

namespace SmartSensorFlow.Admin.Worker;

public class PaddleWebhookProcessor(
    Channel<PaddleWebhookMessage> queue,
    ILogger<PaddleWebhookProcessor> logger,
    IServiceProvider services) : IHostedLifecycleService
{
    private Task? _workerTask;
    private CancellationTokenSource _cts = new();

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _workerTask = Task.Run(() => RunAsync(_cts.Token), cancellationToken);
        return Task.CompletedTask;
    }

    public async Task RunAsync(CancellationToken cancellationToken)
    {
        try
        {
            await foreach (var message in queue.Reader.ReadAllAsync(cancellationToken))
            {
                var responseData = JObject.Parse(message.RawBody);
                var webhookEvent = responseData["event_type"]?.ToString();
                if (webhookEvent is not null)
                {
                    switch (webhookEvent)
                    {
                        case "subscription.activated":
                            await ProcessSubscription(responseData);
                            break;
                        case "transaction.completed":
                            await ProcessTransaction(responseData);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError("Exception thrown in worker: {e}", e.Message);
        }
    }

    private async Task ProcessSubscription(JObject responseData)
    {
        using var scope = services.CreateScope();
        var queryHandler = scope.ServiceProvider.GetRequiredService<IQueryHandler>();

        var paddleSubscriptionId = responseData["data"]?["id"]?.ToString();
        if (paddleSubscriptionId is null)
        {
            logger.LogError("Subscription parsing failed - no paddle subscription id at path [data][id]");
            return;
        }
        var currentSubscription = await queryHandler.GetSubscriptionByPaddleSubscriptionId(paddleSubscriptionId);

        if (currentSubscription is not null)
        {
            logger.LogError("Subscription with ID {subID} already in DB - not adding again (for now)", currentSubscription);
            return;
        }
        var productId = IntFromString(responseData["data"]?["custom_data"]?["productId"]?.ToString());
        var orgId = IntFromString(responseData["data"]?["custom_data"]?["orgId"]?.ToString());

        if (productId is null || orgId is null)
        {
            logger.LogError("No orgId or productId set, so bad custom data");
            return;
        }

        var billingCycle = responseData["data"]?["billing_cycle"]?["interval"]?.ToString();
        if (billingCycle is null)
        {
            logger.LogError("No billing cycle set in payload");
            return;
        }

        var billingCycleEnum = billingCycle == "month" ? BillingCycle.Monthly : BillingCycle.Yearly;

        var firstBilledAt = responseData["data"]?["first_billed_at"]?.ToString();
        var nextBilledAt = responseData["data"]?["next_billed_at"]?.ToString();

        if (firstBilledAt is null || nextBilledAt is null)
        {
            logger.LogError("No billing period set in payload");
            return;
        }

        var price = IntFromString(responseData["data"]?["items"]?[0]?["price"]?["unit_price"]?["amount"]?.ToString());
        var currency = responseData["data"]?["items"]?[0]?["price"]?["unit_price"]?["currency_code"]?.ToString();

        if (price is null || currency is null)
        {
            logger.LogError("No price or currency set in payload");
            return;
        }

        await queryHandler.AddSubscription(
            productId.Value,
            orgId.Value,
            paddleSubscriptionId,
            billingCycleEnum,
            price.Value,
            currency,
            DateTime.Parse(nextBilledAt).ToUniversalTime(),
            DateTime.Parse(firstBilledAt).ToUniversalTime());
    }

    private async Task ProcessTransaction(JObject responseData)
    {
        logger.LogInformation("Transaction: {responseData}", responseData);
        return;
    }

    public Task StoppingAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        return _workerTask ?? Task.CompletedTask;
    }

    public Task StartedAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    public Task StoppedAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    public Task StartingAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    
    private static int? IntFromString(string? s)
    {
        if (s is null)
        {
            return null;
        }

        if (int.TryParse(s, out var value))
        {
            return value;
        }
        return 0;
    }
}
