namespace SmartSensorFlow.Admin.Worker.Helpers;

public static class DataExtensions
{
    public static decimal DecimalFromString(this string? s)
    {
        if (string.IsNullOrEmpty(s))
        {
            return 0M;
        }

        if (decimal.TryParse(s, out var value))
        {
            return value;
        }
        return 0M;
    }

    public static ulong LongFromString(this string? s)
    {
         if (s is null)
        {
            return 0;
        }

        if (ulong.TryParse(s, out var value))
        {
            return value;
        }
        return 0;
    }

    public static int IntFromString(this string? s)
    {
         if (s is null)
        {
            return 0;
        }

        if (int.TryParse(s, out var value))
        {
            return value;
        }
        return 0;
    }
}