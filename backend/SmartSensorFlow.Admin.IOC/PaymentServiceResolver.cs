using Microsoft.Extensions.DependencyInjection;
using SmartSensorFlow.Admin.Common.Enums;
using SmartSensorFlow.Admin.Integration.Paystack.Services;

namespace SmartSensorFlow.Admin.IOC;

public class PaymentServiceResolver(IServiceProvider serviceProvider) : IPaymentServiceResolver
{
    private readonly Dictionary<ProductType, IPaystackService> _data = [];

    public IPaystackService this[ProductType key]
    {
        get
        {
            if (_data.TryGetValue(key, out var value))
            {
                return value;
            }
            throw new KeyNotFoundException($"Key '{key}' not found.");
        }
    }

    public void RegisterPaystackService<TProductClass>(ProductType key)
        where TProductClass : IPaystackService
    {
        if (_data.ContainsKey(key))
        {
            throw new Exception($"Key {key} has already been registered");
        }

        var instance = ActivatorUtilities.CreateInstance<TProductClass>(serviceProvider);
        _data[key] = instance;
    }
}
