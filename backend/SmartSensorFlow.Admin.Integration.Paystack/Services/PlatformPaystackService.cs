using SmartSensorFlow.Admin.Integration.Paystack.Config;

namespace SmartSensorFlow.Admin.Integration.Paystack.Services;

public class PlatformPaystackService(PaystackPlatformConfiguration paystackPlatformConfiguration) 
    : PaystackServiceBase
{
    protected override string GetCallbackUrlBase()
    {
        return paystackPlatformConfiguration.CallbackUrlBase;
    }

    protected override string GetSecretKey()
    {
        return paystackPlatformConfiguration.SecretKey;
    }
}