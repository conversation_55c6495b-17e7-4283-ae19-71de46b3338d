namespace SmartSensorFlow.Admin.Integration.Paystack.Services;

public interface IPaystackService
{
    Task<string> GeneratePaymentLink(
        string emailAddress,
        string currency,
        string paystackPlanId,
        int userId,
        int organisationId,
        int productId);

    Task<bool> VerifyPayment(string referenceNumber);

    Task<string> GenerateSubscriptionManagementLink(string subscriptionId);

    bool VerifyWebhookEvent(string headerValue, string body);

    Task<string> GetSubscription(string subscriptionId);
}
