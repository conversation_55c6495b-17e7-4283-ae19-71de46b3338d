using SmartSensorFlow.Admin.Integration.Paystack.Config;

namespace SmartSensorFlow.Admin.Integration.Paystack.Services;

public class SupportPaystackService(PaystackSupportConfiguration paystackSupportConfiguration)
    : PaystackServiceBase
{
    protected override string GetCallbackUrlBase()
    {
        return paystackSupportConfiguration.CallbackUrlBase;
    }

    protected override string GetSecretKey()
    {
        return paystackSupportConfiguration.SecretKey;
    }
}