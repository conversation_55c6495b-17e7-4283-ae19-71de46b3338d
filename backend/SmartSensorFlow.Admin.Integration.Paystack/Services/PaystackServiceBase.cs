using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json.Linq;
using SmartSensorFlow.Admin.Integration.Paystack.Models;

namespace SmartSensorFlow.Admin.Integration.Paystack.Services;

public abstract class PaystackServiceBase : IPaystackService
{
    private readonly HttpClient _httpClient;
    private static readonly JsonSerializerOptions _jsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    protected abstract string GetSecretKey();
    protected abstract string GetCallbackUrlBase();

    public PaystackServiceBase()
    {
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", GetSecretKey());
        _httpClient.BaseAddress = new Uri("https://api.paystack.co/");
    }

    public async Task<string> GeneratePaymentLink(
        string emailAddress,
        string currency,
        string paystackPlanId,
        int userId,
        int organisationId,
        int productId)
    {
        var data = new GeneratePaymentLinkModel()
        {
            Email = emailAddress,
            Currency = currency,
            CallbackUrl = GetCallbackUrlBase(),
            MetaData = new GeneratePaymentLinkMetadataModel()
            {
                UserId = userId,
                OrganisationId = organisationId,
                ProductId = productId
            },
            Plan = paystackPlanId
        };
        var jsonData = JsonSerializer.Serialize(data, _jsonSerializerOptions);
        var httpContent = new StringContent(jsonData, Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync("transaction/initialize", httpContent);

        var content = await response.Content.ReadAsStringAsync();
        var responseData = JObject.Parse(content);

        if (responseData["status"]?.Value<bool>() != true)
        {
            throw new Exception("Error generating payment token");
        }
        var secureURL = responseData.SelectToken("$.data.authorization_url")?.ToString() ?? throw new Exception("No secure URL in json payload");
        return secureURL;
    }

    public async Task<bool> VerifyPayment(string referenceNumber)
    {
        var response = await _httpClient.GetAsync("transaction/verify/" + referenceNumber);

        var content = await response.Content.ReadAsStringAsync();
        var responseData = JObject.Parse(content);

        var status = responseData["data"]?["status"]?.ToString();

        // Probably log this somewhere
        if (status is not null && status == "success")
        {
            return true;
        }
        return false;
    }

    public async Task<string> GenerateSubscriptionManagementLink(string subscriptionId)
    {
        var response = await _httpClient.GetAsync($"subscription/{subscriptionId}/manage/link");
        var content = await response.Content.ReadAsStringAsync();
        var responseData = JObject.Parse(content);

        if (responseData["status"]?.Value<bool>() != true)
        {
            throw new Exception("Error generating subscription management link");
        }
        var secureURL = responseData.SelectToken("$.data.link")?.ToString() ?? throw new Exception("No secure URL in json payload");
        return secureURL;
    }

    public bool VerifyWebhookEvent(string headerValue, string body)
    {
        using var hmacsha512 = new HMACSHA512(Encoding.UTF8.GetBytes(GetSecretKey()));
        var hashedBody = hmacsha512.ComputeHash(Encoding.UTF8.GetBytes(body));
        var result = BitConverter.ToString(hashedBody).Replace("-", string.Empty);
        return result.ToLower().Equals(headerValue);
    }

    public async Task<string> GetSubscription(string subscriptionId)
    {
        var response = await _httpClient.GetAsync("subscription/" + subscriptionId);
        var content = await response.Content.ReadAsStringAsync();
        return content;
    }
}
