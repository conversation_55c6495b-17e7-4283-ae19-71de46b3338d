using System.Text.Json.Serialization;

namespace SmartSensorFlow.Admin.Integration.Paystack.Models;

public class GeneratePaymentLinkModel
{
    public required string Email { get; set; }

    [JsonPropertyName("callback_url")]
    public required string CallbackUrl { get; set; }

    public required string Currency { get; set; }

    public decimal? Amount { get; set; }

    public string? Plan { get; set; }

    [JsonPropertyName("metadata")]
    public required GeneratePaymentLinkMetadataModel MetaData { get; set; }
}
