#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Configuration
PROJECT_DIR="./SmartSensorFlow.Admin.Api"
PROJECT_FILE="SmartSensorFlow.Admin.Api.csproj"
PUBLISH_DIR="publish"
ZIP_OUTPUT="app.zip"

log() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
    exit 1
}

# Step 2: Clean up existing publish directory and zip file
if [ -d "$PUBLISH_DIR" ]; then
    log "Removing existing publish directory: $PUBLISH_DIR"
    rm -rf "$PUBLISH_DIR" || error "Failed to remove $PUBLISH_DIR"
fi

if [ -f "$ZIP_OUTPUT" ]; then
    log "Removing existing zip file: $ZIP_OUTPUT"
    rm -f "$ZIP_OUTPUT" || error "Failed to remove $ZIP_OUTPUT"
fi

# Step 3: Run dotnet publish
log "Publishing project using dotnet..."
dotnet publish "$PROJECT_DIR"/"$PROJECT_FILE" -c Release -r linux-x64 --self-contained false -o "$PUBLISH_DIR" || error "dotnet publish failed"

# Step 5: Zip the contents
log "Zipping published output to $ZIP_OUTPUT"
zip -r "$ZIP_OUTPUT" . -i "$PUBLISH_DIR/*" || error "Failed to create ZIP archive"

log "✅ Done successfully!"
