import React, { useEffect, useState } from "react";
import { apiService } from "../lib/api.service";

// This will be a Wrapper component that will provide context to the entire app
// The context will do an API call every 5 min to refresh the token
// If the token is expired, it will redirect to the login page

interface SecurityContextType {
  authenticated: boolean;
  refreshToken: () => Promise<void>;
  checkAuthAndRedirect: () => Promise<boolean>;
  logout: () => Promise<void>;
}

export const SecurityContext = React.createContext<SecurityContextType>({
  authenticated: false,
  refreshToken: async () => {},
  checkAuthAndRedirect: async () => false,
  logout: async () => {},
});

export const useSecurity = () => {
  return React.useContext(SecurityContext);
};

interface SecurityProviderProps {
  children: React.ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({
  children,
}) => {
  const [authenticated, setAuthenticated] = useState(false);

  const refreshToken = async () => {
    try {
      const response = await apiService.refreshToken();
      if (response.success) {
        // Token refreshed successfully
        setAuthenticated(true);
        console.log("Token refreshed successfully");
      } else if (response.requiresLogin) {
        // Refresh token invalid/expired, user needs to login
        setAuthenticated(false);
        console.log("Authentication required - refresh token invalid");
        // Here you could redirect to login page or show login modal
        window.location.href = "https://auth.local.ssf:8443/"; //Local
        // window.location.href = "https://identity.smartsensorflow.io/"; //Live
      }
    } catch (error) {
      // Network error or other unexpected error
      console.error("Token refresh failed:", error);
      setAuthenticated(false);
    }
  };

  const checkAuthAndRedirect = async (): Promise<boolean> => {
    try {
      const response = await apiService.refreshToken();
      if (response.success) {
        setAuthenticated(true);
        return true;
      } else if (response.requiresLogin) {
        setAuthenticated(false);
        // Redirect to login page
        console.log("Redirecting to login - authentication required");
        window.location.href = "https://auth.local.ssf:8443/"; //Local
        // window.location.href = "https://identity.smartsensorflow.io/"; //Live
        return false;
      }
      return false;
    } catch (error) {
      console.error("Authentication check failed:", error);
      setAuthenticated(false);
      return false;
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
      setAuthenticated(false);
      console.log("Logged out successfully");
      window.location.href = "https://auth.local.ssf:8443/"; //Local
      // window.location.href = "https://identity.smartsensorflow.io/"; //Live
    } catch (error) {
      console.error("Logout failed:", error);
      setAuthenticated(false);
      window.location.href = "https://auth.local.ssf:8443/"; //Local
      // window.location.href = "https://identity.smartsensorflow.io/"; //Live
    }
  };

  useEffect(() => {
    // Check authentication status immediately on mount
    refreshToken();

    // Set up interval to refresh token every 5 minutes
    const interval = setInterval(
      () => {
        refreshToken();
        console.log("Periodic token refresh");
      },
      1000 * 60 * 5
    ); // 5 min

    return () => clearInterval(interval);
  }, []);

  return (
    <SecurityContext.Provider
      value={{ authenticated, refreshToken, checkAuthAndRedirect, logout }}
    >
      {children}
    </SecurityContext.Provider>
  );
};
