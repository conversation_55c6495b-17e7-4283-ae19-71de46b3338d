import React, { createContext, useContext, useEffect, useState } from "react";
import { useUserStore } from "../lib/stores/user.store";
import { useOrganisationStore } from "../lib/stores/organisation.store";
import { useNotificationStore } from "../lib/stores/notification.store";
import { useSecurity } from "./SecurityWrapper";

interface StateContextType {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

const StateContext = createContext<StateContextType>({
  isInitialized: false,
  isLoading: false,
  error: null,
  refreshData: async () => {},
});

export const useAppState = () => {
  return useContext(StateContext);
};

interface StateProviderProps {
  children: React.ReactNode;
}

export const StateProvider: React.FC<StateProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { authenticated } = useSecurity();
  const { fetchUser, isLoading: userLoading } = useUserStore();
  const { fetchOrganisations, isLoading: orgLoading } = useOrganisationStore();
  const {
    refreshFromUserStore,
    refreshSuspensions,
    isLoading: notificationLoading,
  } = useNotificationStore();

  const initializeAppData = async () => {
    if (!authenticated) {
      setIsInitialized(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch user data first (this includes invites)
      await fetchUser();

      // Update notification store with user data
      refreshFromUserStore();

      // Then fetch organization data
      await fetchOrganisations();

      // Refresh suspension notifications after organizations are loaded
      refreshSuspensions();

      setIsInitialized(true);
      console.log("App state initialized successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to initialize app data";
      setError(errorMessage);
      console.error("Failed to initialize app state:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = async () => {
    if (!authenticated) return;

    setError(null);
    try {
      // Fetch user data first (includes invites)
      await fetchUser();

      // Update notification store with fresh user data
      refreshFromUserStore();

      // Fetch organization data
      await fetchOrganisations();

      // Refresh suspension notifications after organizations are loaded
      refreshSuspensions();

      console.log("App data refreshed successfully");
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to refresh app data";
      setError(errorMessage);
      console.error("Failed to refresh app data:", err);
    }
  };

  // Initialize data when authentication status changes or on mount
  useEffect(() => {
    if (authenticated && !isInitialized && !isLoading) {
      console.log(
        "Initializing app data - authenticated:",
        authenticated,
        "isInitialized:",
        isInitialized
      );
      initializeAppData();
    } else if (!authenticated) {
      setIsInitialized(false);
      setError(null);
    }
  }, [authenticated, isInitialized, isLoading]);

  // Combine loading states
  const combinedLoading =
    isLoading || userLoading || orgLoading || notificationLoading;

  return (
    <StateContext.Provider
      value={{
        isInitialized,
        isLoading: combinedLoading,
        error,
        refreshData,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};
