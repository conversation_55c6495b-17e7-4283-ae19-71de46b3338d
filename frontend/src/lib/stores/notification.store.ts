import { create } from 'zustand';
import { apiService, PendingInviteResponse, AccountStatus } from '../api.service';
import { useUserStore } from './user.store';
import { useOrganisationStore } from './organisation.store';

interface SuspensionNotification {
  organisationId: number;
  organisationName: string;
  status: AccountStatus;
}

interface NotificationState {
  pendingInvites: PendingInviteResponse[];
  suspensions: SuspensionNotification[];
  dismissedSuspensions: Set<number>;
  inviteCount: number;
  suspensionCount: number;
  totalNotificationCount: number;
  isLoading: boolean;
  error: string | null;
  fetchPendingInvites: () => Promise<void>;
  acceptInvite: (inviteKey: string) => Promise<void>;
  rejectInvite: (inviteKey: string, rejectReason?: string) => Promise<void>;
  dismissSuspension: (organisationId: number) => void;
  clearError: () => void;
  refreshFromUserStore: () => void;
  refreshSuspensions: () => void;
}

// Helper functions for localStorage
const getDismissedSuspensions = (): Set<number> => {
  try {
    const dismissed = localStorage.getItem('dismissedSuspensions');
    return dismissed ? new Set(JSON.parse(dismissed)) : new Set();
  } catch {
    return new Set();
  }
};

const saveDismissedSuspensions = (dismissed: Set<number>): void => {
  try {
    localStorage.setItem('dismissedSuspensions', JSON.stringify([...dismissed]));
  } catch {
    // Ignore localStorage errors
  }
};

export const useNotificationStore = create<NotificationState>((set, get) => ({
  pendingInvites: [],
  suspensions: [],
  dismissedSuspensions: getDismissedSuspensions(),
  inviteCount: 0,
  suspensionCount: 0,
  totalNotificationCount: 0,
  isLoading: false,
  error: null,

  fetchPendingInvites: async () => {
    // Get invites from user store since they come from the same /me endpoint
    get().refreshFromUserStore();
  },

  refreshFromUserStore: () => {
    const userStore = useUserStore.getState();
    const state = get();
    const inviteCount = userStore.inviteCount;
    const totalCount = inviteCount + state.suspensionCount;

    set({
      pendingInvites: userStore.invites,
      inviteCount,
      totalNotificationCount: totalCount,
      isLoading: false,
      error: null,
    });
  },

  refreshSuspensions: () => {
    const orgStore = useOrganisationStore.getState();
    const state = get();
    const allSuspensions: SuspensionNotification[] = [];

    orgStore.organisations.forEach(org => {
      const isSuspended = org.userStatus === AccountStatus.Suspended ||
                         (typeof org.userStatus === "string" && org.userStatus === "Suspended");

      if (!org.isOwner && isSuspended) {
        allSuspensions.push({
          organisationId: org.id,
          organisationName: org.name,
          status: org.userStatus
        });
      }
    });

    // Filter out dismissed suspensions
    const visibleSuspensions = allSuspensions.filter(
      suspension => !state.dismissedSuspensions.has(suspension.organisationId)
    );

    const suspensionCount = visibleSuspensions.length;
    const totalCount = state.inviteCount + suspensionCount;

    set({
      suspensions: visibleSuspensions,
      suspensionCount,
      totalNotificationCount: totalCount,
    });
  },

  dismissSuspension: (organisationId: number) => {
    const state = get();
    const newDismissed = new Set(state.dismissedSuspensions);
    newDismissed.add(organisationId);

    // Save to localStorage
    saveDismissedSuspensions(newDismissed);

    // Update state and recalculate counts
    set({ dismissedSuspensions: newDismissed });

    // Refresh suspensions to update the visible list
    get().refreshSuspensions();
  },

  acceptInvite: async (inviteKey: string) => {
    try {
      await apiService.acceptOrganisationInvite(inviteKey);

      // Refresh user data to get updated invites list from backend
      await useUserStore.getState().refreshInvites();

      // Update notification store from user store
      get().refreshFromUserStore();
    } catch (error) {
      console.error('Failed to accept invite:', error);

      // Provide more helpful error messages based on the error type
      let errorMessage = 'Failed to accept invite';

      if (error instanceof Error) {
        if (error.message.includes('500')) {
          errorMessage = 'Server error occurred. The invite system may be temporarily unavailable.';
        } else if (error.message.includes('404')) {
          errorMessage = 'Invite not found or may have expired.';
        } else if (error.message.includes('400')) {
          errorMessage = 'Invalid invite. Please check and try again.';
        } else {
          errorMessage = error.message;
        }
      }

      set({ error: errorMessage });
      throw error; // Re-throw so the component can handle it
    }
  },

  rejectInvite: async (inviteKey: string, rejectReason?: string) => {
    try {
      await apiService.rejectOrganisationInvite(inviteKey, rejectReason);

      // Refresh user data to get updated invites list from backend
      await useUserStore.getState().refreshInvites();

      // Update notification store from user store
      get().refreshFromUserStore();
    } catch (error) {
      console.error('Failed to reject invite:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to reject invite',
      });
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
