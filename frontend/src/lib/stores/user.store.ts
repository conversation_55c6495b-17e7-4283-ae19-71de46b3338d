import { create } from 'zustand';
import { apiService, PendingInviteResponse } from '../api.service';
import { getProfilePictureUrl } from '../utils/profilePicture';

interface UserState {
  firstName: string;
  lastName: string;
  emailAddress: string;
  profilePicture?: string;
  invites: PendingInviteResponse[];
  inviteCount: number;
  isLoading: boolean;
  fetchUser: () => Promise<void>;
  setUser: (user: { firstName: string; lastName: string; emailAddress: string; profilePicture?: string; invites?: PendingInviteResponse[] }) => void;
  refreshInvites: () => Promise<void>;
  updateProfile: (data: { firstName: string; lastName: string; emailAddress: string }) => Promise<void>;
  updateProfilePicture: (profilePictureBase64: string) => Promise<void>;
  removeProfilePicture: () => Promise<void>;
  getProfilePictureUrl: () => string | undefined;
}

export const useUserStore = create<UserState>((set, get) => ({
  firstName: '',
  lastName: '',
  emailAddress: '',
  profilePicture: undefined,
  invites: [],
  inviteCount: 0,
  isLoading: false,

  fetchUser: async () => {
    set({ isLoading: true });
    try {
      const userData = await apiService.getCurrentUser();

      set({
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        emailAddress: userData.emailAddress || '',
        profilePicture: userData.profilePicture,
        invites: userData.invites || [],
        inviteCount: userData.invites?.length || 0,
        isLoading: false,
      });
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      set({ isLoading: false });
    }
  },

  setUser: (user) => {
    set({
      firstName: user.firstName,
      lastName: user.lastName,
      emailAddress: user.emailAddress,
      profilePicture: user.profilePicture,
      invites: user.invites || [],
      inviteCount: user.invites?.length || 0,
    });
  },

  refreshInvites: async () => {
    // Refresh the entire user data since invites come from the same endpoint
    await get().fetchUser();
  },

  updateProfile: async (data) => {
    try {
      await apiService.updateProfile(data);
      // Update local state
      set({
        firstName: data.firstName,
        lastName: data.lastName,
        emailAddress: data.emailAddress,
      });
      // Refresh user data to get any server-side updates
      await get().fetchUser();
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  },

  updateProfilePicture: async (profilePictureBase64) => {
    try {
      await apiService.updateProfilePicture(profilePictureBase64);
      // Refresh user data to get the updated profile picture URL
      await get().fetchUser();
    } catch (error) {
      console.error('Failed to update profile picture:', error);
      throw error;
    }
  },

  removeProfilePicture: async () => {
    try {
      await apiService.removeProfilePicture();
      // Update local state to remove profile picture
      set({ profilePicture: undefined });
      // Refresh user data to confirm removal
      await get().fetchUser();
    } catch (error) {
      console.error('Failed to remove profile picture:', error);
      throw error;
    }
  },

  getProfilePictureUrl: () => {
    const { profilePicture } = get();
    return getProfilePictureUrl(profilePicture);
  },
}));