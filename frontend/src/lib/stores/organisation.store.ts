import { create } from 'zustand';
import { apiService, OrganisationResponse, GetOrganisationResponse, SubscriptionResponse, SubscriptionStatus, OrganisationState as OrganisationStatusEnum, AccountStatus } from '../api.service';

interface OrganisationWithSubscription extends OrganisationResponse {
  subscriptions: SubscriptionResponse[];
  hasActiveSubscription: boolean;
}

interface OrganisationState {
  organisations: OrganisationWithSubscription[];
  selectedOrganisation: OrganisationWithSubscription | null;
  isLoading: boolean;
  isLoadingSubscriptions: boolean;
  isRefreshingSelected: boolean;
  error: string | null;

  // Actions
  fetchOrganisations: () => Promise<void>;
  fetchOrganisationSubscriptions: (organisationId: number) => Promise<SubscriptionResponse[]>;
  refreshSelectedOrganisation: () => Promise<void>;
  createOrganisation: (name: string) => Promise<OrganisationWithSubscription>;
  setSelectedOrganisation: (organisation: OrganisationWithSubscription | null) => void;
  addModuleToOrganisation: (organisationId: number, moduleId: number) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useOrganisationStore = create<OrganisationState>((set, get) => ({
  organisations: [],
  selectedOrganisation: null,
  isLoading: false,
  isLoadingSubscriptions: false,
  isRefreshingSelected: false,
  error: null,

  fetchOrganisationSubscriptions: async (organisationId: number) => {
    try {
      const response = await apiService.getSubscriptions(organisationId);
      return response.subscriptions || [];
    } catch (error) {
      console.error('Failed to fetch subscriptions for organisation:', organisationId, error);
      return [];
    }
  },

  fetchOrganisations: async () => {
    set({ isLoading: true, error: null });
    try {
      const response: GetOrganisationResponse = await apiService.getOrganisations();
      const baseOrganisations = response.organisations || [];

      // Fetch subscriptions for each organisation
      const organisationsWithSubscriptions: OrganisationWithSubscription[] = await Promise.all(
        baseOrganisations.map(async (org) => {
          const subscriptions = await get().fetchOrganisationSubscriptions(org.id);
          const hasActiveSubscription = subscriptions.some(sub => sub.status === SubscriptionStatus.Active);

          return {
            ...org,
            subscriptions,
            hasActiveSubscription
          };
        })
      );

      set({
        organisations: organisationsWithSubscriptions,
        isLoading: false,
        error: null
      });

      // Auto-select first organisation if none selected and organisations exist
      const { selectedOrganisation } = get();
      if (!selectedOrganisation && organisationsWithSubscriptions.length > 0) {
        set({ selectedOrganisation: organisationsWithSubscriptions[0] });
      }
    } catch (error) {
      console.error('Failed to fetch organisations:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch organisations'
      });
    }
  },

  refreshSelectedOrganisation: async () => {
    const { selectedOrganisation } = get();
    if (!selectedOrganisation) return;

    set({ isRefreshingSelected: true, error: null });
    try {
      // Fetch fresh data for the selected organisation
      const response: GetOrganisationResponse = await apiService.getOrganisations();
      const baseOrganisations = response.organisations || [];

      // Find the current selected organisation in the fresh data
      const updatedOrg = baseOrganisations.find(org => org.id === selectedOrganisation.id);
      if (!updatedOrg) {
        throw new Error('Selected organisation not found');
      }

      // Revert to original logic for now
      const subscriptions = await get().fetchOrganisationSubscriptions(updatedOrg.id);
      const hasActiveSubscription = subscriptions.some(sub => sub.status === SubscriptionStatus.Active);

      const refreshedOrganisation: OrganisationWithSubscription = {
        ...updatedOrg,
        subscriptions,
        hasActiveSubscription
      };

      // Update the selected organisation and the organisations list
      const { organisations } = get();
      const updatedOrganisations = organisations.map(org =>
        org.id === selectedOrganisation.id ? refreshedOrganisation : org
      );

      set({
        selectedOrganisation: refreshedOrganisation,
        organisations: updatedOrganisations,
        isRefreshingSelected: false,
        error: null
      });
    } catch (error) {
      console.error('Failed to refresh selected organisation:', error);
      set({
        isRefreshingSelected: false,
        error: error instanceof Error ? error.message : 'Failed to refresh selected organisation'
      });
    }
  },

  createOrganisation: async (name: string) => {
    // Don't set isLoading to true to avoid triggering StateWrapper re-renders
    // that could close the dialog
    set({ error: null });
    try {
      const createResponse = await apiService.createOrganisation(name);

      // Create a minimal organization object for immediate use
      // Don't fetch all organizations yet - wait until payment is completed
      const newOrganisation: OrganisationWithSubscription = {
        id: createResponse.organisationId,
        name: name,
        isOwner: true,
        organisationStatus: OrganisationStatusEnum.Inactive, // New organizations start as inactive
        userStatus: AccountStatus.Active, // User status for the organization
        createdAt: new Date().toISOString(),
        modules: [],
        subscriptions: [],
        hasActiveSubscription: false
      };

      set({
        selectedOrganisation: newOrganisation,
        error: null
      });

      return newOrganisation;
    } catch (error) {
      console.error('Failed to create organisation:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create organisation'
      });
      throw error;
    }
  },

  setSelectedOrganisation: (organisation: OrganisationWithSubscription | null) => {
    const { selectedOrganisation: currentSelected } = get();

    // Only refresh if we're switching to a different organisation
    if (organisation && (!currentSelected || currentSelected.id !== organisation.id)) {
      set({ selectedOrganisation: organisation });
      // Trigger refresh to get latest data for the newly selected organisation
      get().refreshSelectedOrganisation();
    } else {
      set({ selectedOrganisation: organisation });
    }
  },

  addModuleToOrganisation: async (organisationId: number, moduleId: number) => {
    try {
      await apiService.addModuleToOrganisation(organisationId, moduleId);

      // If the module was added to the currently selected organisation, refresh it immediately
      const { selectedOrganisation } = get();
      if (selectedOrganisation && selectedOrganisation.id === organisationId) {
        await get().refreshSelectedOrganisation();
      } else {
        // Otherwise, refresh all organisations to keep data consistent
        await get().fetchOrganisations();
      }
    } catch (error) {
      console.error('Failed to add module to organisation:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to add module to organisation'
      });
      throw error;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      organisations: [],
      selectedOrganisation: null,
      isLoading: false,
      isLoadingSubscriptions: false,
      isRefreshingSelected: false,
      error: null
    });
  }
}));
