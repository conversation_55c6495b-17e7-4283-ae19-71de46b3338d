// Helper function to convert base64 string to data URL for image display
export const getProfilePictureUrl = (profilePicture?: string): string | undefined => {
  if (!profilePicture) return undefined;
  
  // If it's already a data URL or regular URL, return as is
  if (profilePicture.startsWith('data:') || profilePicture.startsWith('http')) {
    return profilePicture;
  }
  
  // If it's a base64 string, convert to data URL
  // Assume JPEG format by default, but this could be enhanced to detect format
  return `data:image/jpeg;base64,${profilePicture}`;
};
