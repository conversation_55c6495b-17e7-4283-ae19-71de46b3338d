import { FC } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>barHeader,
  SidebarRail,
} from "../ui/sidebar";
import TeamSwitcher from "./TeamSwitcher";
import NavMain from "./NavMain";
import NavProjects from "./NavProjects";
import NavPolicies from "./NavPolicies";
import NavUser from "./NavUser";

const AppSidebar: FC = () => {
  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavMain />
        <NavProjects />
      </SidebarContent>
      <SidebarFooter>
        <NavPolicies />
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};

export default AppSidebar;
