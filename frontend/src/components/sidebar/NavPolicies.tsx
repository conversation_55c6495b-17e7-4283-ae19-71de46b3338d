import { FC } from "react";
import { FileText, Shield, RefreshCw, DollarSign } from "lucide-react";
import { Link, useLocation } from "@tanstack/react-router";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";

const NavPolicies: FC = () => {
  const location = useLocation();

  // Helper function to check if a route is active
  const isActive = (route: string) => {
    return location.pathname === route;
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="text-[10px] font-medium">
        Policies
      </SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            asChild
            isActive={isActive("/terms&conditions")}
            className="h-6 px-2"
          >
            <Link
              to="/terms&conditions"
              className="flex items-center gap-1.5 w-full text-[10px]"
            >
              <FileText className="size-2.5" />
              <span>Terms & Conditions</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarMenuItem>
          <SidebarMenuButton
            asChild
            isActive={isActive("/privacypolicy")}
            className="h-6 px-2"
          >
            <Link
              to="/privacypolicy"
              className="flex items-center gap-1.5 w-full text-[10px]"
            >
              <Shield className="size-2.5" />
              <span>Privacy Policy</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarMenuItem>
          <SidebarMenuButton
            asChild
            isActive={isActive("/refundpolicy")}
            className="h-6 px-2"
          >
            <Link
              to="/refundpolicy"
              className="flex items-center gap-1.5 w-full text-[10px]"
            >
              <RefreshCw className="size-2.5" />
              <span>Refund Policy</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
        <SidebarMenuItem>
          <SidebarMenuButton
            asChild
            isActive={isActive("/pricing")}
            className="h-6 px-2"
          >
            <Link
              to="/pricing"
              className="flex items-center gap-1.5 w-full text-[10px]"
            >
              <DollarSign className="size-2.5" />
              <span>Pricing</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
};

export default NavPolicies;
