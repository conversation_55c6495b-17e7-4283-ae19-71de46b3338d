import { FC } from "react";
import {
  <PERSON>tings,
  BarChart3,
  Users,
  Package,
  ShoppingCart,
  Lock,
} from "lucide-react";
import { Link, useLocation } from "@tanstack/react-router";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";
import { useOrganisationStore } from "../../lib/stores/organisation.store";

const NavProjects: FC = () => {
  const location = useLocation();
  const { organisations, selectedOrganisation } = useOrganisationStore();

  // Helper function to check if a route/component is active
  const isActive = (route: string, component?: string) => {
    if (component) {
      return (
        location.pathname === route &&
        new URLSearchParams(location.search).get("component") === component
      );
    }
    return (
      location.pathname === route &&
      !new URLSearchParams(location.search).get("component")
    );
  };

  // Check if user has active subscription access
  // This matches the same logic used in HomeComponent
  const hasActivePayingAccess = (() => {
    if (selectedOrganisation) {
      return selectedOrganisation.hasActiveSubscription;
    }
    return organisations.some((org) => org.hasActiveSubscription);
  })();

  // Only show organization and products sections if user has active subscription
  const hasOrganizations = organisations.length > 0 && hasActivePayingAccess;

  return (
    <>
      {hasOrganizations && (
        <>
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs">
              {selectedOrganisation
                ? selectedOrganisation.name
                : "Organizations"}
            </SidebarGroupLabel>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/organization", "admin")}
                  size="sm"
                >
                  <Link
                    to="/organization"
                    search={{ component: "admin" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <Settings className="size-3" />
                    <span>Admin</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/organization", "dashboard")}
                  size="sm"
                >
                  <Link
                    to="/organization"
                    search={{ component: "dashboard" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <BarChart3 className="size-3" />
                    <span>Dashboard</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/organization", "users")}
                  size="sm"
                >
                  <Link
                    to="/organization"
                    search={{ component: "users" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <Users className="size-3" />
                    <span>Users</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/organization", "permissions")}
                  size="sm"
                >
                  <Link
                    to="/organization"
                    search={{ component: "permissions" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <Lock className="size-3" />
                    <span>Permissions</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel className="text-xs">Products</SidebarGroupLabel>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/products", "my-products")}
                  size="sm"
                >
                  <Link
                    to="/products"
                    search={{ component: "my-products" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <Package className="size-3" />
                    <span>
                      My Products
                      {selectedOrganisation
                        ? ` (${selectedOrganisation.modules.length})`
                        : ""}
                    </span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={isActive("/products", "all-products")}
                  size="sm"
                >
                  <Link
                    to="/products"
                    search={{ component: "all-products" }}
                    className="flex items-center gap-2 w-full text-xs"
                  >
                    <ShoppingCart className="size-3" />
                    <span>All Products</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        </>
      )}
    </>
  );
};

export default NavProjects;
