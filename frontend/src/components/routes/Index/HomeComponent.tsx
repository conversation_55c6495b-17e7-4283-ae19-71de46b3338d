import { FC, useMemo } from "react";
import { useOrganisationStore } from "../../../lib/stores/organisation.store";
import InactiveComponent from "./components/InactiveComponent";
import ActiveComponent from "./components/ActiveComponent";

interface HomeProperties {
  firstName: string;
  lastName: string;
}

const HomeComponent: FC<HomeProperties> = ({ firstName, lastName }) => {
  const { organisations, selectedOrganisation } = useOrganisationStore();

  // Check if the selected organization has an active subscription
  // If no organization is selected, check if any organization has an active subscription
  const hasActivePayingAccess = useMemo(() => {
    console.log("HomeComponent - selectedOrganisation:", selectedOrganisation);
    console.log("HomeComponent - organisations:", organisations);

    // Debug: Show all organizations with their IDs and subscription status
    organisations.forEach((org) => {
      console.log(
        `Org: ${org.name} (ID: ${org.id}) - hasActiveSubscription: ${org.hasActiveSubscription}, subscriptions:`,
        org.subscriptions
      );
    });

    if (selectedOrganisation) {
      // Check the selected organization's subscription status
      console.log(
        `HomeComponent - Selected org ${selectedOrganisation.name} hasActiveSubscription: ${selectedOrganisation.hasActiveSubscription}`
      );
      return selectedOrganisation.hasActiveSubscription;
    }

    // Fallback: if no organization is selected, check if any organization has an active subscription
    const hasAnyActive = organisations.some((org) => org.hasActiveSubscription);
    console.log(
      `HomeComponent - No selected org, any org has active subscription: ${hasAnyActive}`
    );
    return hasAnyActive;
  }, [selectedOrganisation, organisations]);

  console.log(
    `HomeComponent - Final decision: hasActivePayingAccess = ${hasActivePayingAccess}`
  );

  return (
    <div>
      <h1 className="text-2xl font-bold tracking-tight w-full text-center pb-10">
        Welcome{" "}
        <span className="text-blue-500">
          {firstName} {lastName}
        </span>
      </h1>
      {hasActivePayingAccess ? <ActiveComponent /> : <InactiveComponent />}
    </div>
  );
};

export default HomeComponent;
