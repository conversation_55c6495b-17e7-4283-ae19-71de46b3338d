import { FC, useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { Badge } from "../../../ui/badge";
import { <PERSON><PERSON> } from "../../../ui/button";
import { Alert, AlertDescription, AlertTitle } from "../../../ui/alert";
import {
  Sparkles,
  Zap,
  Shield,
  BarChart3,
  Wifi,
  Package,
  ExternalLink,
  ArrowRight,
  Loader2,
} from "lucide-react";
import { apiService, ModuleResponse } from "../../../../lib/api.service";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import { toast } from "sonner";
import PartnerDialogComponent from "./PartnerDialogComponent";

const ActiveComponent: FC = () => {
  const [modules, setModules] = useState<ModuleResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { selectedOrganisation, addModuleToOrganisation } =
    useOrganisationStore();

  useEffect(() => {
    const fetchModules = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiService.getModules();
        setModules(response.modules || []);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch modules"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchModules();
  }, []);

  const handleAddModule = async (module: ModuleResponse) => {
    if (!selectedOrganisation) {
      toast.error("Please select an organization first");
      return;
    }

    const isOwned = selectedOrganisation.modules.some(
      (orgModule: ModuleResponse) => orgModule.id === module.id
    );
    if (isOwned) {
      toast.error("Module already added to organization");
      return;
    }

    try {
      await addModuleToOrganisation(
        selectedOrganisation.id,
        parseInt(module.id)
      );
      toast.success(`${module.name} added to ${selectedOrganisation.name}`);
    } catch (error) {
      toast.error("Failed to add module to organization");
    }
  };

  const handleOpenModule = (module: ModuleResponse) => {
    if (module.attributes.urlLive) {
      window.open(`https://${module.attributes.urlLive}`, "_blank");
    }
  };

  // Get featured modules (first 3 modules)
  const featuredModules = modules.slice(0, 3);

  // Get categories based on actual modules
  const categories = [
    { name: "All Modules", count: modules.length, icon: Package },
    {
      name: "Analytics",
      count: modules.filter((m) => m.name.toLowerCase().includes("analytic"))
        .length,
      icon: BarChart3,
    },
    {
      name: "Monitoring",
      count: modules.filter((m) => m.name.toLowerCase().includes("monitor"))
        .length,
      icon: Shield,
    },
    {
      name: "Integration",
      count: modules.filter((m) => m.name.toLowerCase().includes("integrat"))
        .length,
      icon: Wifi,
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading modules...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>Error loading modules: {error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Banner */}
      <Alert className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950 dark:to-indigo-950">
        <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        <AlertTitle className="text-blue-800 dark:text-blue-200 text-lg font-semibold">
          🚀 Smart Sensor Module Marketplace
        </AlertTitle>
        <AlertDescription className="text-blue-700 dark:text-blue-300">
          <div className="space-y-3 mt-2">
            <p className="text-base">
              Discover and integrate cutting-edge sensor modules to enhance your
              Smart Sensor Flow experience. From environmental monitoring to
              industrial automation, find the perfect modules for your needs.
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant="secondary"
                className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                <Zap className="w-3 h-3 mr-1" />
                Easy Integration
              </Badge>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                <Shield className="w-3 h-3 mr-1" />
                Enterprise Ready
              </Badge>
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
              >
                <BarChart3 className="w-3 h-3 mr-1" />
                Real-time Analytics
              </Badge>
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Join the Partner Program */}
      <Alert className="border-purple-300 bg-gradient-to-r from-purple-100 to-pink-100 dark:border-purple-700 dark:from-purple-900 dark:to-pink-900 flex flex-col items-center py-6">
        <AlertTitle className="text-xl font-bold text-center mb-1">
          🤝 Join the Partner Program
        </AlertTitle>
        <AlertDescription className="text-purple-700 dark:text-purple-300 text-center">
          <div className="flex justify-baseline items-center gap-3">
            <p className="text-base font-medium">
              Share your sensor modules &amp; earn revenue.
            </p>
            <PartnerDialogComponent />
          </div>
        </AlertDescription>
      </Alert>

      {/* Categories Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Browse by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <Card
                key={category.name}
                className="hover:shadow-md transition-shadow cursor-pointer"
              >
                <CardContent className="p-4 text-center">
                  <IconComponent className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <h3 className="font-medium">{category.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {category.count} modules
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Featured Modules */}
      <div>
        <h2 className="text-xl font-semibold mb-4">
          {featuredModules.length > 0
            ? "Featured Modules"
            : "Available Modules"}
        </h2>
        {featuredModules.length === 0 ? (
          <Card className="p-8 text-center">
            <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Modules Available</h3>
            <p className="text-muted-foreground">
              Check back later for new modules to enhance your Smart Sensor Flow
              experience.
            </p>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredModules.map((module) => {
              const isOwned =
                selectedOrganisation?.modules.some(
                  (orgModule: ModuleResponse) => orgModule.id === module.id
                ) || false;

              return (
                <Card
                  key={module.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Package className="h-6 w-6 text-primary" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-lg">
                            {module.name}
                          </CardTitle>
                          <Badge
                            variant={isOwned ? "default" : "outline"}
                            className={
                              isOwned
                                ? "bg-green-100 text-green-800 hover:bg-green-200 mt-1"
                                : "mt-1"
                            }
                          >
                            {isOwned ? "Owned" : "Available"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <CardDescription className="mt-2">
                      {module.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Module Details */}
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p>Created by: {module.createdBy}</p>
                        <p>
                          Created:{" "}
                          {new Date(module.createdAt).toLocaleDateString()}
                        </p>
                        {module.attributes.urlLive && (
                          <p className="text-primary">Live URL Available</p>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        {isOwned ? (
                          <Button
                            onClick={() => handleOpenModule(module)}
                            className="flex-1"
                            disabled={!module.attributes.urlLive}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Open Module
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleAddModule(module)}
                            className="flex-1"
                            variant="outline"
                            disabled={!selectedOrganisation}
                          >
                            <ArrowRight className="h-4 w-4 mr-2" />
                            Add to Organization
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Coming Soon Section */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950 border-purple-200 dark:border-purple-800">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-purple-800 dark:text-purple-200">
            More Modules Coming Soon!
          </CardTitle>
          <CardDescription className="text-purple-700 dark:text-purple-300 text-base">
            We're constantly expanding our module library. Stay tuned for
            exciting new additions including AI-powered analytics, advanced IoT
            integrations, and custom enterprise solutions.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Button
            variant="outline"
            className="border-purple-300 text-purple-800 hover:bg-purple-100 dark:border-purple-700 dark:text-purple-200 dark:hover:bg-purple-900"
          >
            Get Notified
            <Sparkles className="h-4 w-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ActiveComponent;
