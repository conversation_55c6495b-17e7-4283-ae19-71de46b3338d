import { FC } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../ui/dialog";
import { cn } from "../../../../lib/utils";
import { buttonVariants, Button } from "../../../ui/button";
import { Check } from "lucide-react";

/**
 * A helper component to safely render text with embedded links.
 * This avoids using dangerouslySetInnerHTML.
 */
const BenefitText: FC<{ text: string }> = ({ text }) => {
  const parts = text.split(/(revenue share|Marketplace)/g);
  return (
    <span className="text-foreground">
      {parts.map((part, i) => {
        if (part === "revenue share" || part === "Marketplace") {
          return (
            <a
              key={i}
              href="#"
              className="text-blue-600 hover:underline font-medium"
            >
              {part}
            </a>
          );
        }
        return part;
      })}
    </span>
  );
};

const PartnerDialogComponent: FC = () => {
  const benefits = [
    "24/7 priority support",
    "Potential to earn revenue share",
    "Eligibility to apply to the Marketplace",
    "Eligibility to access our Success Team",
  ];

  return (
    <Dialog>
      <DialogTrigger
        className={`${cn(
          buttonVariants({ variant: "default" })
        )} cursor-pointer`}
      >
        Join Now
      </DialogTrigger>
      <DialogContent className="sm:max-w-4xl p-0 overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Left Side: Content */}
          <div className="p-8 flex flex-col">
            <DialogHeader className="text-left">
              <DialogTitle className="text-2xl lg:text-3xl font-bold">
                Join our Partner Program
              </DialogTitle>
              <DialogDescription className="text-base pt-2 text-muted-foreground">
                Agencies and freelancers can enjoy free access to exclusive
                benefits:
              </DialogDescription>
            </DialogHeader>

            <ul className="space-y-3 my-6">
              {benefits.map((benefit) => (
                <li key={benefit} className="flex items-start gap-3">
                  <Check className="h-5 w-5 text-black mt-1 flex-shrink-0 dark:text-white" />
                  <BenefitText text={benefit} />
                </li>
              ))}
            </ul>

            <div className="flex flex-col sm:flex-row items-center gap-3 mt-auto pt-6">
              <Button className="w-full sm:w-auto flex-1">
                Become a Partner
              </Button>
              <Button variant="outline" className="w-full sm:w-auto flex-1">
                View Benefits
              </Button>
            </div>

            <DialogFooter className="!justify-start mt-8">
              <p className="text-xs text-muted-foreground text-left">
                By joining you accept the Partner Program{" "}
                <a href="#" className="underline hover:text-primary">
                  Terms of Use
                </a>
                . Availability of benefits and features varies by Partner level.
              </p>
            </DialogFooter>
          </div>

          {/* Right Side: Image Placeholder */}
          <div className="hidden md:flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-100 dark:from-blue-950 dark:to-purple-950 p-8">
            <div className="w-full h-full rounded-lg flex items-center justify-center">
              <img
                src="/images/partner.jpg"
                alt="Partner Program"
                className="max-w-full max-h-full object-cover rounded-lg bg-transparent"
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PartnerDialogComponent;
