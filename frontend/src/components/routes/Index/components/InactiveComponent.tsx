import { FC, useState } from "react";
import { <PERSON><PERSON> } from "../../../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { CreateOrganisationDialog } from "../../../ui/custom/create-organisation-dialog";
import InactiveOrganisationBanner from "../../../ui/custom/inactive-organisation-banner";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import { Building2, CreditCard, Users } from "lucide-react";

const InactiveComponent: FC = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const { organisations, selectedOrganisation, setSelectedOrganisation } =
    useOrganisationStore();

  // Check if user has a selected organization with inactive subscription
  const hasSelectedInactiveOrg =
    selectedOrganisation && !selectedOrganisation.hasActiveSubscription;

  // Check if user has organizations but no selected organization (fallback to first inactive)
  const hasUnselectedInactiveOrgs =
    !selectedOrganisation &&
    organisations.length > 0 &&
    organisations.every((org) => !org.hasActiveSubscription);

  // Get the organization to show for upgrade (selected or first inactive)
  const orgToUpgrade =
    selectedOrganisation ||
    organisations.find((org) => !org.hasActiveSubscription);

  // Determine if we should show the inactive organization UI
  const showInactiveOrgUI = hasSelectedInactiveOrg || hasUnselectedInactiveOrgs;

  const handleCreateComplete = () => {
    setIsCreateDialogOpen(false);
    // The organization store will automatically refresh and update the UI
  };

  const handleUpgradeComplete = () => {
    setIsUpgradeDialogOpen(false);
    // The organization store will automatically refresh and update the UI
  };

  const handleContinueSetup = () => {
    if (orgToUpgrade) {
      // Set the inactive organization as selected for the payment flow
      setSelectedOrganisation(orgToUpgrade);
      setIsUpgradeDialogOpen(true);
    }
  };

  return (
    <div className="h-full w-full flex justify-center items-center p-6">
      <div className="w-full max-w-2xl space-y-6">
        {/* Show banner for inactive organizations */}
        {showInactiveOrgUI && orgToUpgrade && (
          <InactiveOrganisationBanner
            organisationName={orgToUpgrade.name}
            onContinueSetup={handleContinueSetup}
          />
        )}

        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">
              {showInactiveOrgUI
                ? "Upgrade Your Organization"
                : "Get Started with Smart Sensor Flow"}
            </CardTitle>
            <CardDescription className="text-lg">
              {showInactiveOrgUI
                ? "Choose a subscription plan to activate your organization"
                : "You need an active subscription to access the platform features"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="flex flex-col items-center space-y-2">
                <Building2 className="h-8 w-8 text-blue-500" />
                <h3 className="font-semibold">
                  {showInactiveOrgUI
                    ? "Your Organization"
                    : "Create Organization"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {showInactiveOrgUI
                    ? "Activate your existing organization"
                    : "Set up your organization to get started"}
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <CreditCard className="h-8 w-8 text-green-500" />
                <h3 className="font-semibold">Choose a Plan</h3>
                <p className="text-sm text-muted-foreground">
                  Select a subscription plan that fits your needs
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <Users className="h-8 w-8 text-purple-500" />
                <h3 className="font-semibold">Start Collaborating</h3>
                <p className="text-sm text-muted-foreground">
                  Invite team members and manage your sensors
                </p>
              </div>
            </div>

            <div className="text-center space-y-3">
              {showInactiveOrgUI ? (
                <div className="space-y-2">
                  <Button
                    onClick={handleContinueSetup}
                    size="lg"
                    className="w-full md:w-auto cursor-pointer"
                  >
                    Upgrade {orgToUpgrade?.name}
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={() => setIsCreateDialogOpen(true)}
                  size="lg"
                  className="w-full md:w-auto"
                >
                  Create Your First Organization
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dialog for creating new organizations */}
      <CreateOrganisationDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onComplete={handleCreateComplete}
      />

      {/* Dialog for upgrading existing organizations */}
      <CreateOrganisationDialog
        open={isUpgradeDialogOpen}
        onOpenChange={setIsUpgradeDialogOpen}
        onComplete={handleUpgradeComplete}
        initialStep={2} // Skip organization creation, go directly to payment
      />
    </div>
  );
};

export default InactiveComponent;
