import { FC, useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader } from "../../ui/card";
import { BrainCircuit, DollarSign } from "lucide-react";
import { apiService, ProductResponse } from "../../../lib/api.service";
import { toast } from "sonner";
import { Button } from "../../ui/button";

const PricingComponent: FC = () => {
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        const response = await apiService.getProducts();
        setProducts(response.products);
        console.log("Products loaded:", response.products);
      } catch (error) {
        console.error("Failed to fetch products:", error);
        toast.error("Failed to load subscription plans. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  if (isLoadingProducts) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 mb-6">
            <DollarSign className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold tracking-tight">Pricing Plans</h1>
          </div>
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">
              Loading subscription plans...
            </h3>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 mb-6">
            <DollarSign className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold tracking-tight">Pricing Plans</h1>
          </div>
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">
              No subscription plans available
            </h3>
            <p className="text-sm text-muted-foreground">
              Please contact support or try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center space-y-4 mb-8">
        <div className="flex items-center justify-center gap-2 mb-6">
          <DollarSign className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold tracking-tight">Pricing Plans</h1>
        </div>
        <h2 className="text-xl font-semibold">
          Choose a plan that suits your needs
        </h2>
        <p className="text-sm text-muted-foreground">
          For custom plans, contact us at{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-primary hover:underline"
          >
            <EMAIL>
          </a>
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {products.map((product) => (
          <Card key={product.id} className="w-full flex flex-col relative">
            <CardHeader>
              <div className="flex justify-between items-start">
                <span className="text-2xl font-semibold">{product.name}</span>
                <span className="text-lg font-bold text-primary">
                  {product.price}
                </span>
              </div>
              <CardDescription className="uppercase text-xs font-thin tracking-wide text-blue-500">
                {product.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <BrainCircuit size={12} className="text-blue-500" />
                  {product.attributes.gateways} gateways
                </li>
                <li className="flex items-center gap-2">
                  <BrainCircuit size={12} className="text-blue-500" />
                  {product.attributes.assets} assets
                </li>
                <li className="flex items-center gap-2">
                  <BrainCircuit size={12} className="text-blue-500" />
                  {product.attributes.dataPoints} data points/month
                </li>
                {product.attributes.support && (
                  <li className="flex items-center gap-2">
                    <BrainCircuit size={12} className="text-blue-500" />
                    Support included
                  </li>
                )}
                {product.attributes.whiteLabel && (
                  <li className="flex items-center gap-2">
                    <BrainCircuit size={12} className="text-blue-500" />
                    White Label
                  </li>
                )}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex flex-col items-center py-8 space-y-4 max-w-2xl mx-auto text-center">
        <h3 className="text-lg font-semibold">Can't find the right plan?</h3>
        <p className="text-sm text-muted-foreground">
          We have custom solutions that can be tailored to your specific needs.
          Contact our team to discuss enterprise options and volume discounts.
        </p>
        <Button
          variant="secondary"
          className="w-full md:w-auto"
          onClick={() =>
            window.open("mailto:<EMAIL>", "_blank")
          }
        >
          Contact us for custom solutions
        </Button>
      </div>
    </div>
  );
};

export default PricingComponent;
