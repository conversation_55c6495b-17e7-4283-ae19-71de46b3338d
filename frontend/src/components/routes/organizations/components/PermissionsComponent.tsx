import { FC, useState, useEffect } from "react";
import { <PERSON><PERSON> } from "../../../ui/button";
import { Input } from "../../../ui/input";
import { Label } from "../../../ui/label";
import { Textarea } from "../../../ui/textarea";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../ui/dropdown-menu";
import { Badge } from "../../../ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../../ui/tabs";
import { Checkbox } from "../../../ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import {
  apiService,
  PermissionResponse,
  RoleResponse,
  UserResponse,
  CreateRoleRequest,
} from "../../../../lib/api.service";
import { toast } from "sonner";
import { MoreHorizontal, Shield, Users, Settings } from "lucide-react";

// Create Role Tab Component
interface CreateRoleTabProps {
  organisationId: number;
  onRoleCreated: () => void;
  isOwner: boolean;
  isLoading: boolean;
}

const CreateRoleTab: FC<CreateRoleTabProps> = ({
  organisationId,
  onRoleCreated,
  isOwner,
  isLoading,
}) => {
  const [roleName, setRoleName] = useState("");
  const [roleDescription, setRoleDescription] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async () => {
    if (!roleName.trim()) {
      toast.error("Please enter a role name");
      return;
    }

    setIsCreating(true);
    try {
      const request: CreateRoleRequest = {
        roleName: roleName.trim(),
        roleDescription: roleDescription.trim(),
        organisationId,
      };

      await apiService.createRole(request);
      toast.success("Role created successfully");
      setRoleName("");
      setRoleDescription("");
      onRoleCreated();
    } catch (error) {
      console.error("Failed to create role:", error);
      if (error instanceof Error && error.message.includes("not implemented")) {
        toast.error(
          "Create role functionality is not yet available - backend implementation needed"
        );
      } else {
        toast.error("Failed to create role");
      }
    } finally {
      setIsCreating(false);
    }
  };

  if (!isOwner) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-lg font-semibold text-muted-foreground">
              Access Restricted
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Only organization owners can create roles.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Role</CardTitle>
        <CardDescription>
          Create a new role for this organization. You can assign permissions to
          it in the next tab.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="roleName">Role Name</Label>
            <Input
              id="roleName"
              value={roleName}
              onChange={(e) => setRoleName(e.target.value)}
              placeholder="Enter role name"
              disabled={isLoading || isCreating}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="roleDescription">Role Description</Label>
            <Textarea
              id="roleDescription"
              value={roleDescription}
              onChange={(e) => setRoleDescription(e.target.value)}
              placeholder="Enter role description"
              rows={3}
              disabled={isLoading || isCreating}
            />
          </div>
          <Button
            onClick={handleCreate}
            disabled={isLoading || isCreating || !roleName.trim()}
            className="w-fit"
          >
            {isCreating ? "Creating..." : "Create Role"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Assign Permissions to Role Tab Component
interface AssignPermissionsTabProps {
  roles: RoleResponse[];
  permissions: PermissionResponse[];
  onPermissionAssigned: () => void;
  isOwner: boolean;
  isLoading: boolean;
}

const AssignPermissionsTab: FC<AssignPermissionsTabProps> = ({
  roles,
  permissions,
  onPermissionAssigned,
  isOwner,
  isLoading,
}) => {
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);
  const [isAssigning, setIsAssigning] = useState(false);

  const handlePermissionToggle = (permissionId: number) => {
    setSelectedPermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleAssignPermissions = async () => {
    if (!selectedRoleId) {
      toast.error("Please select a role");
      return;
    }

    if (selectedPermissions.length === 0) {
      toast.error("Please select at least one permission");
      return;
    }

    setIsAssigning(true);
    try {
      // Assign each permission to the role
      for (const permissionId of selectedPermissions) {
        await apiService.addPermissionToRole(
          parseInt(selectedRoleId),
          permissionId
        );
      }

      toast.success(
        `${selectedPermissions.length} permission(s) assigned successfully`
      );
      setSelectedPermissions([]);
      onPermissionAssigned();
    } catch (error) {
      console.error("Failed to assign permissions:", error);
      if (error instanceof Error && error.message.includes("not implemented")) {
        toast.error(
          "Permission assignment functionality is not yet available - backend implementation needed"
        );
      } else {
        toast.error("Failed to assign permissions");
      }
    } finally {
      setIsAssigning(false);
    }
  };

  if (!isOwner) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-lg font-semibold text-muted-foreground">
              Access Restricted
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Only organization owners can assign permissions to roles.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (roles.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Settings className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-semibold text-muted-foreground">
              No roles available
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Create roles first in the previous tab to assign permissions.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Assign Permissions to Role</CardTitle>
        <CardDescription>
          Select a role and assign permissions to it.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="roleSelect">Select Role</Label>
          <Select value={selectedRoleId} onValueChange={setSelectedRoleId}>
            <SelectTrigger>
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              {roles.map((role) => (
                <SelectItem key={role.id} value={role.id.toString()}>
                  {role.roleName} - {role.roleDescription}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedRoleId && (
          <div className="space-y-2">
            <Label>Available Permissions</Label>
            <div className="border rounded-md p-4 max-h-64 overflow-y-auto space-y-2">
              {permissions.map((permission) => (
                <div
                  key={permission.id}
                  className="flex items-center space-x-2"
                >
                  <Checkbox
                    id={`permission-${permission.id}`}
                    checked={selectedPermissions.includes(permission.id)}
                    onCheckedChange={() =>
                      handlePermissionToggle(permission.id)
                    }
                    disabled={isLoading || isAssigning}
                  />
                  <Label
                    htmlFor={`permission-${permission.id}`}
                    className="text-sm font-normal flex-1"
                  >
                    <div>
                      <div className="font-medium">
                        {permission.permissionName}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {permission.permissionDescription}
                      </div>
                    </div>
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        <Button
          onClick={handleAssignPermissions}
          disabled={
            isLoading ||
            isAssigning ||
            !selectedRoleId ||
            selectedPermissions.length === 0
          }
          className="w-fit"
        >
          {isAssigning
            ? "Assigning..."
            : `Assign ${selectedPermissions.length} Permission(s)`}
        </Button>
      </CardContent>
    </Card>
  );
};

// Assign Role to Users Tab Component
interface AssignRoleToUsersTabProps {
  users: UserResponse[];
  roles: RoleResponse[];
  onRoleAssigned: () => void;
  isOwner: boolean;
  isLoading: boolean;
}

const AssignRoleToUsersTab: FC<AssignRoleToUsersTabProps> = ({
  users,
  roles,
  onRoleAssigned,
  isOwner,
  isLoading,
}) => {
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedRoleId, setSelectedRoleId] = useState<string>("");
  const [isAssigning, setIsAssigning] = useState(false);

  const handleAssignRole = async () => {
    if (!selectedUserId || !selectedRoleId) {
      toast.error("Please select both a user and a role");
      return;
    }

    setIsAssigning(true);
    try {
      await apiService.assignRoleToUser(
        parseInt(selectedUserId),
        parseInt(selectedRoleId)
      );
      toast.success("Role assigned successfully");
      setSelectedUserId("");
      setSelectedRoleId("");
      onRoleAssigned();
    } catch (error) {
      console.error("Failed to assign role:", error);
      if (error instanceof Error && error.message.includes("not implemented")) {
        toast.error(
          "Role assignment functionality is not yet available - backend implementation needed"
        );
      } else {
        toast.error("Failed to assign role");
      }
    } finally {
      setIsAssigning(false);
    }
  };

  if (!isOwner) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-lg font-semibold text-muted-foreground">
              Access Restricted
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Only organization owners can assign roles to users.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (roles.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Settings className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-semibold text-muted-foreground">
              No roles available
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Create roles and assign permissions first before assigning to
              users.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (users.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Users className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-semibold text-muted-foreground">
              No users available
            </h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Invite users to this organization first.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Assign Role to User</CardTitle>
          <CardDescription>
            Select a user and assign a role to them.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userSelect">Select User</Label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a user" />
                </SelectTrigger>
                <SelectContent>
                  {users
                    .filter((user) => !user.isOwner) // Don't allow assigning roles to owners
                    .map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.firstName} {user.lastName} ({user.emailAddress})
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="roleSelect">Select Role</Label>
              <Select value={selectedRoleId} onValueChange={setSelectedRoleId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.roleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button
            onClick={handleAssignRole}
            disabled={
              isLoading || isAssigning || !selectedUserId || !selectedRoleId
            }
            className="w-fit"
          >
            {isAssigning ? "Assigning..." : "Assign Role"}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current User Assignments</CardTitle>
          <CardDescription>
            View current role assignments for users in this organization.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Current Roles</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">
                    {user.firstName} {user.lastName}
                  </TableCell>
                  <TableCell>{user.emailAddress}</TableCell>
                  <TableCell>
                    {user.isOwner ? (
                      <Badge variant="default">Owner</Badge>
                    ) : (
                      <Badge variant="secondary">Member</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    {!user.isOwner && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem disabled>
                            <Settings className="mr-2 h-4 w-4" />
                            Manage Roles (Coming Soon)
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

const PermissionsComponent: FC = () => {
  const { selectedOrganisation } = useOrganisationStore();
  const [permissions, setPermissions] = useState<PermissionResponse[]>([]);
  const [roles, setRoles] = useState<RoleResponse[]>([]);
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("create-role");

  // Check if current user is organization owner
  const isOrganisationOwner = selectedOrganisation?.isOwner || false;

  const fetchData = async () => {
    if (!selectedOrganisation) return;

    setIsLoading(true);
    try {
      console.log(
        "Fetching permissions data for organization:",
        selectedOrganisation.id
      );

      // Fetch data individually to better handle errors
      let permissionsResponse, rolesResponse, usersResponse;

      try {
        permissionsResponse = await apiService.getAllPermissions();
        console.log("Permissions fetched successfully:", permissionsResponse);
      } catch (error) {
        console.error("Failed to fetch permissions:", error);
        permissionsResponse = { permissions: [] };
      }

      try {
        rolesResponse = await apiService.getRoles(selectedOrganisation.id);
        console.log("Roles fetched successfully:", rolesResponse);
      } catch (error) {
        console.error("Failed to fetch roles:", error);
        rolesResponse = { roles: [] };
      }

      try {
        usersResponse = await apiService.getOrganisationUsers(
          selectedOrganisation.id
        );
        console.log("Users fetched successfully:", usersResponse);
      } catch (error) {
        console.error("Failed to fetch users:", error);
        usersResponse = { users: [] };
      }

      setPermissions(permissionsResponse.permissions || []);
      setRoles(rolesResponse.roles || []);
      setUsers(usersResponse.users || []);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      toast.error("Failed to load permissions data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [selectedOrganisation]);

  if (!selectedOrganisation) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Organization Permissions
            </h1>
            <p className="text-muted-foreground">
              Please select an organization to manage permissions
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied message for non-owners
  if (!isOrganisationOwner) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Organization Permissions
            </h1>
            <p className="text-muted-foreground">
              Manage roles and permissions for {selectedOrganisation.name}
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-lg font-semibold text-muted-foreground">
                Access Restricted
              </h3>
              <p className="mt-1 text-sm text-muted-foreground">
                Only organization owners can manage roles and permissions.
              </p>
              <p className="mt-1 text-sm text-muted-foreground">
                Contact the organization owner to request changes.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Organization Permissions
          </h1>
          <p className="text-muted-foreground">
            Manage roles and permissions for {selectedOrganisation.name}
          </p>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="create-role">1. Create Role</TabsTrigger>
          <TabsTrigger value="assign-permissions">
            2. Assign Permissions
          </TabsTrigger>
          <TabsTrigger value="assign-users">3. Assign to Users</TabsTrigger>
        </TabsList>

        <TabsContent value="create-role" className="space-y-4">
          <CreateRoleTab
            organisationId={selectedOrganisation.id}
            onRoleCreated={fetchData}
            isOwner={isOrganisationOwner}
            isLoading={isLoading}
          />

          {/* Show existing roles */}
          {roles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Existing Roles</CardTitle>
                <CardDescription>
                  Roles that have been created for this organization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Created By</TableHead>
                      <TableHead>Created At</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">
                          {role.roleName}
                        </TableCell>
                        <TableCell>{role.roleDescription}</TableCell>
                        <TableCell>{role.createdBy}</TableCell>
                        <TableCell>
                          {new Date(role.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="assign-permissions" className="space-y-4">
          <AssignPermissionsTab
            roles={roles}
            permissions={permissions}
            onPermissionAssigned={fetchData}
            isOwner={isOrganisationOwner}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="assign-users" className="space-y-4">
          <AssignRoleToUsersTab
            users={users}
            roles={roles}
            onRoleAssigned={fetchData}
            isOwner={isOrganisationOwner}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PermissionsComponent;
