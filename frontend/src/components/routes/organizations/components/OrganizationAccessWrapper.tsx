import { FC, ReactNode } from "react";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import { AccountStatus } from "../../../../lib/api.service";
import AccessDeniedBanner from "../../../ui/custom/access-denied-banner";

interface OrganizationAccessWrapperProps {
  children: ReactNode;
}

const OrganizationAccessWrapper: FC<OrganizationAccessWrapperProps> = ({
  children,
}) => {
  const { selectedOrganisation } = useOrganisationStore();

  if (!selectedOrganisation) {
    return <>{children}</>;
  }

  const isSuspended =
    selectedOrganisation.userStatus === AccountStatus.Suspended ||
    (typeof selectedOrganisation.userStatus === "string" &&
      selectedOrganisation.userStatus === "Suspended");

  const isRemoved =
    selectedOrganisation.userStatus === AccountStatus.Removed ||
    (typeof selectedOrganisation.userStatus === "string" &&
      selectedOrganisation.userStatus === "Removed");

  const hasRestrictedAccess = isSuspended || isRemoved;

  return (
    <div className="w-full h-full">
      {hasRestrictedAccess && (
        <AccessDeniedBanner
          organizationName={selectedOrganisation.name}
          userStatus={selectedOrganisation.userStatus}
        />
      )}

      {hasRestrictedAccess ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            <p className="text-lg font-medium mb-2">Access Restricted</p>
            <p>You cannot view this organization's information.</p>
          </div>
        </div>
      ) : (
        children
      )}
    </div>
  );
};

export default OrganizationAccessWrapper;
