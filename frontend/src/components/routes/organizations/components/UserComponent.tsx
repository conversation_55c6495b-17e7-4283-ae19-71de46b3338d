import { FC, useState, useEffect } from "react";
import { Badge } from "../../../ui/badge";
import { But<PERSON> } from "../../../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "../../../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../../ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../../ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import { Checkbox } from "../../../ui/checkbox";
import {
  UserPlus,
  MoreHorizontal,
  Crown,
  User,
  UserCheck,
  UserX,
  UserMinus,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import { useUserStore } from "../../../../lib/stores/user.store";
import {
  apiService,
  UserResponse,
  AccountStatus,
} from "../../../../lib/api.service";
import { toast } from "sonner";
import Loader from "../../../ui/loader";

const inviteUserSchema = z.object({
  emailAddress: z.string().email("Please enter a valid email address"),
  firstName: z.string().min(1, "First name is required"),
  surname: z.string().min(1, "Last name is required"),
});

type InviteUserFormData = z.infer<typeof inviteUserSchema>;

const UserComponent: FC = () => {
  const { selectedOrganisation } = useOrganisationStore();
  const { emailAddress: currentUserEmail, getProfilePictureUrl } =
    useUserStore();
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isInviteLoading, setIsInviteLoading] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [userToRemove, setUserToRemove] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [confirmRemoval, setConfirmRemoval] = useState(false);

  const form = useForm<InviteUserFormData>({
    resolver: zodResolver(inviteUserSchema),
    defaultValues: {
      emailAddress: "",
      firstName: "",
      surname: "",
    },
  });

  const fetchUsers = async () => {
    if (!selectedOrganisation) return;

    setIsLoading(true);
    try {
      const response = await apiService.getOrganisationUsers(
        selectedOrganisation.id
      );
      setUsers(response.users || []);
    } catch (error) {
      console.error("Failed to fetch users:", error);
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [selectedOrganisation]);

  const handleUserAction = async (
    action: string,
    userId: string,
    userName: string
  ) => {
    if (!selectedOrganisation) return;

    try {
      switch (action) {
        case "suspend":
          await apiService.suspendUserInOrganisation(
            selectedOrganisation.id,
            parseInt(userId)
          );
          toast.success(`${userName} has been suspended`);
          break;
        case "activate":
          await apiService.activateUserInOrganisation(
            selectedOrganisation.id,
            parseInt(userId)
          );
          toast.success(`${userName} has been unsuspended`);
          break;
      }
      fetchUsers(); // Refresh the user list
    } catch (error) {
      console.error(`Failed to ${action} user:`, error);
      toast.error(`Failed to ${action} user`);
    }
  };

  const handleRemoveUser = (userId: string, userName: string) => {
    setUserToRemove({ id: userId, name: userName });
    setIsRemoveDialogOpen(true);
    setConfirmRemoval(false);
  };

  const handleConfirmRemoval = async () => {
    if (!selectedOrganisation || !userToRemove || !confirmRemoval) return;

    try {
      await apiService.removeUserFromOrganisation(
        selectedOrganisation.id,
        parseInt(userToRemove.id)
      );
      toast.success(
        `${userToRemove.name} has been removed from the organization`
      );
      setIsRemoveDialogOpen(false);
      setUserToRemove(null);
      setConfirmRemoval(false);
      fetchUsers(); // Refresh the user list
    } catch (error) {
      console.error("Failed to remove user:", error);
      toast.error("Failed to remove user");
    }
  };

  const handleCancelRemoval = () => {
    setIsRemoveDialogOpen(false);
    setUserToRemove(null);
    setConfirmRemoval(false);
  };

  const handleInvite = async (data: InviteUserFormData) => {
    if (!selectedOrganisation) return;

    setIsInviteLoading(true);
    try {
      const response = await apiService.inviteUserToOrganisation(
        selectedOrganisation.id,
        data.emailAddress,
        data.firstName,
        data.surname
      );

      if (response.success) {
        toast.success("User invited successfully");
        form.reset();
        setIsInviteDialogOpen(false);
        fetchUsers(); // Refresh the user list
      } else {
        toast.error(response.failureReason || "Failed to invite user");
      }
    } catch (error) {
      console.error("Failed to invite user:", error);
      toast.error("Failed to invite user");
    } finally {
      setIsInviteLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    setIsInviteDialogOpen(newOpen);
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const isCurrentUser = (userEmail: string) => {
    return userEmail === currentUserEmail;
  };

  const isUserOwner = (user: UserResponse) => {
    return user.isOwner;
  };

  const isUserStatus = (status: AccountStatus, targetStatus: AccountStatus) => {
    return (
      status === targetStatus ||
      (typeof status === "string" && status === AccountStatus[targetStatus])
    );
  };

  const getStatusBadge = (status: AccountStatus) => {
    // AccountStatus enum values: Pending (0), Active (1), Suspended (2), Removed (3)
    // Backend sends enum as strings due to JsonStringEnumConverter
    const statusString =
      typeof status === "string" ? status : AccountStatus[status];

    if (statusString === "Active" || status === AccountStatus.Active) {
      return (
        <Badge
          variant="default"
          className="bg-green-100 text-green-800 hover:bg-green-200 text-xs"
        >
          <CheckCircle className="mr-1 h-3 w-3" />
          Active
        </Badge>
      );
    }

    if (statusString === "Pending" || status === AccountStatus.Pending) {
      return (
        <Badge
          variant="secondary"
          className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 text-xs"
        >
          <Clock className="mr-1 h-3 w-3" />
          Pending
        </Badge>
      );
    }

    if (statusString === "Suspended" || status === AccountStatus.Suspended) {
      return (
        <Badge variant="destructive" className="text-xs">
          <AlertTriangle className="mr-1 h-3 w-3" />
          Suspended
        </Badge>
      );
    }

    if (statusString === "Removed" || status === AccountStatus.Removed) {
      return (
        <Badge
          variant="outline"
          className="bg-red-100 text-red-800 hover:bg-red-200 text-xs"
        >
          <XCircle className="mr-1 h-3 w-3" />
          Removed
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="text-xs">
        {statusString || status}
      </Badge>
    );
  };

  const canManageUsers = selectedOrganisation?.isOwner;

  if (!selectedOrganisation) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Organization Users
            </h1>
            <p className="text-muted-foreground">
              Please select an organization to manage users
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Organization Users
          </h1>
          <p className="text-muted-foreground">
            Manage users in {selectedOrganisation.name}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Organization Members
              </CardTitle>
              <CardDescription>
                {users.length} {users.length === 1 ? "member" : "members"} in{" "}
                {selectedOrganisation.name}
              </CardDescription>
            </div>
            {canManageUsers && (
              <Button onClick={() => setIsInviteDialogOpen(true)} size="sm">
                <UserPlus className="mr-2 h-4 w-4" />
                Invite User
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader text="Loading members..." />
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold text-muted-foreground">
                No members
              </h3>
              <p className="mt-1 text-sm text-muted-foreground">
                Get started by inviting users to this organization.
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {users.map((user) => {
                const isOwner = isUserOwner(user);
                const isCurrent = isCurrentUser(user.emailAddress);

                return (
                  <div
                    key={user.id}
                    className={`flex items-center justify-between p-4 rounded-lg border transition-colors ${
                      isCurrent
                        ? "bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800"
                        : "bg-card hover:bg-muted/50"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar
                        className={`h-10 w-10 ${isCurrent ? "ring-2 ring-blue-500" : ""}`}
                      >
                        <AvatarImage
                          src={isCurrent ? getProfilePictureUrl() : undefined}
                          alt={`${user.firstName} ${user.lastName}`}
                        />
                        <AvatarFallback
                          className={
                            isOwner
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                          }
                        >
                          {getInitials(user.firstName, user.lastName)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 flex-wrap">
                          <p className="font-medium">
                            {user.firstName} {user.lastName}
                            {isCurrent && (
                              <span className="text-blue-600 text-sm">
                                (You)
                              </span>
                            )}
                          </p>
                          {isOwner && (
                            <Badge
                              variant="default"
                              className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
                            >
                              <Crown className="mr-1 h-3 w-3" />
                              Owner
                            </Badge>
                          )}
                          {!isOwner && (
                            <Badge variant="secondary">
                              <User className="mr-1 h-3 w-3" />
                              Member
                            </Badge>
                          )}
                          <div className="flex gap-1 flex-wrap">
                            {getStatusBadge(user.organisationStatus)}
                            {user.userStatus !== user.organisationStatus &&
                              getStatusBadge(user.userStatus)}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {user.emailAddress}
                        </p>
                      </div>
                    </div>

                    {/* Actions - only show for organization owners managing non-owner users who aren't themselves */}
                    {canManageUsers && !isOwner && !isCurrent && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {/* Show Activate only for suspended users */}
                          {isUserStatus(
                            user.organisationStatus,
                            AccountStatus.Suspended
                          ) && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleUserAction(
                                  "activate",
                                  user.id,
                                  `${user.firstName} ${user.lastName}`
                                )
                              }
                            >
                              <UserCheck className="mr-2 h-4 w-4" />
                              Unsuspend
                            </DropdownMenuItem>
                          )}

                          {/* Show Suspend only for active users */}
                          {isUserStatus(
                            user.organisationStatus,
                            AccountStatus.Active
                          ) && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleUserAction(
                                  "suspend",
                                  user.id,
                                  `${user.firstName} ${user.lastName}`
                                )
                              }
                            >
                              <UserX className="mr-2 h-4 w-4" />
                              Suspend
                            </DropdownMenuItem>
                          )}

                          {/* Show Remove for active and suspended users (not already removed) */}
                          {!isUserStatus(
                            user.organisationStatus,
                            AccountStatus.Removed
                          ) && (
                            <DropdownMenuItem
                              onClick={() =>
                                handleRemoveUser(
                                  user.id,
                                  `${user.firstName} ${user.lastName}`
                                )
                              }
                              className="text-destructive"
                            >
                              <UserMinus className="mr-2 h-4 w-4" />
                              Remove
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invite User Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Invite User to Organization</DialogTitle>
            <DialogDescription>
              Send an invitation to a user to join this organization.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleInvite)}
              className="space-y-4"
            >
              <div className="grid gap-4 py-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="surname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="emailAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsInviteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isInviteLoading}>
                  {isInviteLoading ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Send Invite
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Remove User Confirmation Dialog */}
      <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-destructive">
              <UserMinus className="h-5 w-5" />
              Remove User from Organization
            </DialogTitle>
            <DialogDescription className="text-left">
              You are about to permanently remove{" "}
              <strong>{userToRemove?.name}</strong> from{" "}
              <strong>{selectedOrganisation?.name}</strong>.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-destructive">
                    This action is permanent and cannot be undone
                  </p>
                  <p className="text-sm text-muted-foreground">
                    The user will lose all access to this organization and its
                    resources immediately. If you want to temporarily restrict
                    access, consider suspending the user instead.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="confirm-removal"
                checked={confirmRemoval}
                onCheckedChange={(checked) =>
                  setConfirmRemoval(checked === true)
                }
              />
              <label
                htmlFor="confirm-removal"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I understand this action is permanent and cannot be reversed
              </label>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={handleCancelRemoval}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmRemoval}
              disabled={!confirmRemoval}
            >
              <UserMinus className="mr-2 h-4 w-4" />
              Remove User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserComponent;
