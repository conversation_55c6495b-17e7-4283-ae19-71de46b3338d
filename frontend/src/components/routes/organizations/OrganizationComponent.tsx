import { FC } from "react";
import AdminComponent from "./components/AdminComponent";
import DashBoardComponent from "./components/DashBoardComponent";
import UserComponent from "./components/UserComponent";
import PermissionsComponent from "./components/PermissionsComponent";
import OrganizationAccessWrapper from "./components/OrganizationAccessWrapper";

interface OrganizationProperties {
  activeComponent?: string;
}

const OrganizationComponent: FC<OrganizationProperties> = ({
  activeComponent,
}) => {
  const renderComponent = () => {
    switch (activeComponent) {
      case "admin":
        return <AdminComponent />;
      case "dashboard":
        return <DashBoardComponent />;
      case "users":
        return <UserComponent />;
      case "permissions":
        return <PermissionsComponent />;
      default:
        return <DashBoardComponent />; // Default to dashboard
    }
  };

  return (
    <OrganizationAccessWrapper>
      <div className="w-full h-full">{renderComponent()}</div>
    </OrganizationAccessWrapper>
  );
};

export default OrganizationComponent;
