import { FC } from "react";
import AllProductsComponent from "./components/AllProductsComponent";
import MyProductsComponent from "./components/MyProductsComponent";

interface ProductsProperties {
  activeComponent?: string;
}

const ProductsComponent: FC<ProductsProperties> = ({ activeComponent }) => {
  const renderComponent = () => {
    switch (activeComponent) {
      case "my-products":
        return <MyProductsComponent />;
      case "all-products":
        return <AllProductsComponent />;
      default:
        return <AllProductsComponent />; // Default to all products
    }
  };

  return <div className="w-full h-full">{renderComponent()}</div>;
};

export default ProductsComponent;
