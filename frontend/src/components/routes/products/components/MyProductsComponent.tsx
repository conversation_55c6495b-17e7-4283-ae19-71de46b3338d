import { FC } from "react";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { Badge } from "../../../ui/badge";
import { Button } from "../../../ui/button";
import { ExternalLink, Package } from "lucide-react";

const MyProductsComponent: FC = () => {
  const { selectedOrganisation } = useOrganisationStore();

  const handleModuleClick = (module: { attributes: { urlLive: string } }) => {
    if (module.attributes.urlLive) {
      window.open(`https://${module.attributes.urlLive}`, "_blank");
    }
  };

  if (!selectedOrganisation) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <Package className="h-12 w-12 text-muted-foreground" />
        <p className="text-muted-foreground">
          Please select an organization to view your products
        </p>
      </div>
    );
  }

  if (selectedOrganisation.modules.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <Package className="h-12 w-12 text-muted-foreground" />
        <p className="text-muted-foreground">
          No products owned by this organization
        </p>
        <p className="text-sm text-muted-foreground">
          Browse all products to add modules to your organization
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">My Products</h2>
        <p className="text-muted-foreground">
          Products owned by {selectedOrganisation.name}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {selectedOrganisation.modules.map((module) => (
          <Card
            key={module.id}
            className="relative cursor-pointer hover:shadow-lg transition-shadow"
          >
            <div className="absolute top-4 right-4 z-10">
              <Badge
                variant="default"
                className="bg-green-100 text-green-800 hover:bg-green-200"
              >
                Owned
              </Badge>
            </div>

            <CardHeader className="pb-4">
              <CardTitle className="pr-16">{module.name}</CardTitle>
              <CardDescription className="h-10 overflow-hidden">
                {module.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="flex flex-col space-y-4">
                <div className="text-sm text-muted-foreground">
                  <p>Created by: {module.createdBy}</p>
                  <p>
                    Created: {new Date(module.createdAt).toLocaleDateString()}
                  </p>
                </div>

                <Button
                  onClick={() => handleModuleClick(module)}
                  className="w-full"
                  disabled={!module.attributes.urlLive}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Open Module
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default MyProductsComponent;
