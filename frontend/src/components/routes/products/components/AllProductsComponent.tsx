import { FC, useEffect, useState } from "react";
import { apiService, ModuleResponse } from "../../../../lib/api.service";
import { useOrganisationStore } from "../../../../lib/stores/organisation.store";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../ui/card";
import { Badge } from "../../../ui/badge";
import { Button } from "../../../ui/button";
import { Plus, Package } from "lucide-react";
import { toast } from "sonner";
import Loader from "../../../ui/loader";

const AllProductsComponent: FC = () => {
  const [modules, setModules] = useState<ModuleResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [addingModuleId, setAddingModuleId] = useState<string | null>(null);
  const { selectedOrganisation, addModuleToOrganisation } =
    useOrganisationStore();

  useEffect(() => {
    const fetchModules = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await apiService.getModules();
        setModules(response.modules || []);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch modules"
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchModules();
  }, []);

  const handleAddModule = async (module: ModuleResponse) => {
    if (!selectedOrganisation) {
      toast.error("Please select an organization first");
      return;
    }

    const isOwned = selectedOrganisation.modules.some(
      (orgModule) => orgModule.id === module.id
    );
    if (isOwned) {
      toast.error("Module already added to organization");
      return;
    }

    setAddingModuleId(module.id);
    try {
      await addModuleToOrganisation(
        selectedOrganisation.id,
        parseInt(module.id)
      );
      toast.success(`${module.name} added to ${selectedOrganisation.name}`);
    } catch (error) {
      toast.error("Failed to add module to organization");
    } finally {
      setAddingModuleId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader text="Loading modules..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-destructive">Error loading modules: {error}</p>
      </div>
    );
  }

  if (modules.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <Package className="h-12 w-12 text-muted-foreground" />
        <p className="text-muted-foreground">No modules available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">All Products</h2>
        <p className="text-muted-foreground">
          Browse all available modules and products
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((module) => {
          const isOwned =
            selectedOrganisation?.modules.some(
              (orgModule) => orgModule.id === module.id
            ) || false;

          return (
            <Card
              key={module.id}
              className="relative cursor-pointer hover:shadow-lg transition-shadow"
            >
              <div className="absolute top-4 right-4 z-10">
                <Badge
                  variant={isOwned ? "default" : "outline"}
                  className={
                    isOwned
                      ? "bg-green-100 text-green-800 hover:bg-green-200"
                      : ""
                  }
                >
                  {isOwned ? "Owned" : "Available"}
                </Badge>
              </div>

              <CardHeader className="pb-4">
                <CardTitle className="pr-16">{module.name}</CardTitle>
                <CardDescription className="h-10 overflow-hidden">
                  {module.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="flex flex-col space-y-4">
                  <div className="text-sm text-muted-foreground">
                    <p>Created by: {module.createdBy}</p>
                    <p>
                      Created: {new Date(module.createdAt).toLocaleDateString()}
                    </p>
                  </div>

                  <Button
                    onClick={() => handleAddModule(module)}
                    className="w-full"
                    disabled={
                      isOwned ||
                      addingModuleId === module.id ||
                      !selectedOrganisation
                    }
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    {addingModuleId === module.id
                      ? "Adding..."
                      : isOwned
                        ? "Already Added"
                        : "Add to Organization"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default AllProductsComponent;
