import { Bell } from "lucide-react";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { useNotificationStore } from "../../lib/stores/notification.store";
import { useUserStore } from "../../lib/stores/user.store";
import NotificationList from "./NotificationList";

const NotificationBell = () => {
  const { totalNotificationCount, refreshFromUserStore, refreshSuspensions } =
    useNotificationStore();
  const { refreshInvites } = useUserStore();

  const handleBellClick = async () => {
    // Refresh invite data from the backend when bell is clicked
    await refreshInvites();
    // Update notification store with fresh data
    refreshFromUserStore();
    // Refresh suspension notifications
    refreshSuspensions();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          onClick={handleBellClick}
        >
          <Bell className="h-4 w-4" />
          {totalNotificationCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
              {totalNotificationCount > 9 ? "9+" : totalNotificationCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <NotificationList />
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationBell;
