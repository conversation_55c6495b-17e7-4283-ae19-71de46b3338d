import { useEffect } from "react";
import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Separator } from "../ui/separator";
import { useNotificationStore } from "../../lib/stores/notification.store";
import { useAppState } from "../../context/StateWrapper";
import { toast } from "sonner";

import { Check, X, Users, AlertTriangle } from "lucide-react";
import Loader from "../ui/loader";

// Simple date formatting utility
const formatDistanceToNow = (date: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) return "just now";
  if (diffInMinutes < 60)
    return `${diffInMinutes} minute${diffInMinutes !== 1 ? "s" : ""} ago`;
  if (diffInHours < 24)
    return `${diffInHours} hour${diffInHours !== 1 ? "s" : ""} ago`;
  if (diffInDays < 7)
    return `${diffInDays} day${diffInDays !== 1 ? "s" : ""} ago`;

  return date.toLocaleDateString();
};

const formatTimeUntil = (date: Date) => {
  const now = new Date();
  const diffInMs = date.getTime() - now.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMs < 0) return "expired";
  if (diffInMinutes < 60)
    return `in ${diffInMinutes} minute${diffInMinutes !== 1 ? "s" : ""}`;
  if (diffInHours < 24)
    return `in ${diffInHours} hour${diffInHours !== 1 ? "s" : ""}`;

  return `in ${diffInDays} day${diffInDays !== 1 ? "s" : ""}`;
};

const NotificationList = () => {
  const {
    pendingInvites,
    suspensions,
    totalNotificationCount,
    isLoading,
    error,
    acceptInvite,
    rejectInvite,
    dismissSuspension,
    clearError,
    refreshFromUserStore,
    refreshSuspensions,
  } = useNotificationStore();
  const { refreshData } = useAppState();

  useEffect(() => {
    // Sync notification store with user store data on mount
    refreshFromUserStore();
    refreshSuspensions();
  }, [refreshFromUserStore, refreshSuspensions]);

  const handleAcceptInvite = async (
    inviteKey: string,
    organisationName: string
  ) => {
    try {
      await acceptInvite(inviteKey);
      toast.success(`Successfully joined ${organisationName}`);
      // Refresh app data to update organizations
      await refreshData();
    } catch (error) {
      toast.error("Failed to accept invite");
    }
  };

  const handleRejectInvite = async (
    inviteKey: string,
    organisationName: string
  ) => {
    try {
      await rejectInvite(inviteKey);
      toast.success(`Declined invitation to ${organisationName}`);
    } catch (error) {
      toast.error("Failed to reject invite");
    }
  };

  const handleDismissSuspension = (
    organisationId: number,
    organisationName: string
  ) => {
    dismissSuspension(organisationId);
    toast.success(`Dismissed suspension notification for ${organisationName}`);
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center">
        <Loader text="" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-sm text-red-600 mb-2">{error}</div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            clearError();
            refreshFromUserStore();
            refreshSuspensions();
          }}
        >
          Try Again
        </Button>
      </div>
    );
  }

  if (totalNotificationCount === 0) {
    return (
      <div className="p-4 text-center">
        <div className="flex flex-col items-center gap-2">
          <Users className="h-8 w-8 text-muted-foreground" />
          <div className="text-sm text-muted-foreground">No notifications</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-h-96 overflow-y-auto">
      <div className="p-3 border-b">
        <h3 className="font-semibold text-sm">Notifications</h3>
        <p className="text-xs text-muted-foreground">
          {totalNotificationCount} notification
          {totalNotificationCount !== 1 ? "s" : ""}
        </p>
      </div>

      <div className="p-2 space-y-2">
        {/* Suspension Notifications */}
        {suspensions.length > 0 && (
          <>
            <div className="px-2 py-1">
              <h4 className="text-xs font-medium text-muted-foreground">
                Account Status
              </h4>
            </div>
            {suspensions.map((suspension, index) => (
              <div key={`suspension-${suspension.organisationId}`}>
                <Card className="border-0 shadow-none bg-red-50 dark:bg-red-950">
                  <CardHeader className="p-3 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        Account Suspended
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                        onClick={() =>
                          handleDismissSuspension(
                            suspension.organisationId,
                            suspension.organisationName
                          )
                        }
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Your access to{" "}
                      <strong>{suspension.organisationName}</strong> has been
                      suspended. Please contact the organization owner for more
                      information.
                    </CardDescription>
                  </CardHeader>
                </Card>
                {(index < suspensions.length - 1 ||
                  pendingInvites.length > 0) && <Separator />}
              </div>
            ))}
          </>
        )}

        {/* Invitation Notifications */}
        {pendingInvites.length > 0 && (
          <>
            {suspensions.length > 0 && (
              <div className="px-2 py-1">
                <h4 className="text-xs font-medium text-muted-foreground">
                  Invitations
                </h4>
              </div>
            )}
            {pendingInvites.map((invite, index) => (
              <div key={invite.inviteKey}>
                <Card className="border-0 shadow-none">
                  <CardHeader className="p-3 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {invite.organisationName || "Organization Invitation"}
                    </CardTitle>
                    <CardDescription className="text-xs">
                      {invite.createdAt ? (
                        <>
                          Invited{" "}
                          {formatDistanceToNow(new Date(invite.createdAt))}
                          {invite.firstName && invite.lastName && (
                            <span className="block">
                              From: {invite.firstName} {invite.lastName}
                            </span>
                          )}
                        </>
                      ) : (
                        "You have been invited to join an organization"
                      )}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-3 pt-0">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={() =>
                          handleAcceptInvite(
                            invite.inviteKey,
                            invite.organisationName || "Organization"
                          )
                        }
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Accept
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() =>
                          handleRejectInvite(
                            invite.inviteKey,
                            invite.organisationName || "Organization"
                          )
                        }
                      >
                        <X className="h-3 w-3 mr-1" />
                        Decline
                      </Button>
                    </div>
                    {invite.inviteExpireDate && (
                      <div className="text-xs text-muted-foreground mt-2">
                        Expires{" "}
                        {formatTimeUntil(new Date(invite.inviteExpireDate))}
                      </div>
                    )}
                  </CardContent>
                </Card>
                {index < pendingInvites.length - 1 && <Separator />}
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
};

export default NotificationList;
