import { FC, ReactNode } from "react";
import { SidebarInset, SidebarTrigger } from "../ui/sidebar";
import { Separator } from "../ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../ui/breadcrumb";
import { Link, useLocation } from "@tanstack/react-router";
import AppSidebar from "../sidebar/AppSidebar";
import NotificationBell from "../notifications/NotificationBell";
import { useAppState } from "../../context/StateWrapper";
import { useSecurity } from "../../context/SecurityWrapper";
import Loader from "../ui/loader";

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { isLoading } = useAppState();
  const { authenticated } = useSecurity();

  const getBreadcrumbs = () => {
    const breadcrumbs = [{ title: "Home", href: "/" }];

    const searchParams = new URLSearchParams(location.search);
    const component = searchParams.get("component");

    if (location.pathname === "/organization") {
      breadcrumbs.push({ title: "Organization", href: "/organization" });
      if (component) {
        const componentTitles: Record<string, string> = {
          admin: "Admin",
          dashboard: "Dashboard",
          users: "Users",
        };
        breadcrumbs.push({
          title: componentTitles[component] || component,
          href: `/organization?component=${component}`,
        });
      }
    } else if (location.pathname === "/products") {
      breadcrumbs.push({ title: "Products", href: "/products" });
      if (component) {
        const componentTitles: Record<string, string> = {
          "my-products": "My Products",
          "all-products": "All Products",
        };
        breadcrumbs.push({
          title: componentTitles[component] || component,
          href: `/products?component=${component}`,
        });
      }
    } else if (location.pathname === "/profile") {
      breadcrumbs.push({ title: "Profile", href: "/profile" });
      if (component) {
        const componentTitles: Record<string, string> = {
          profile: "Profile",
          settings: "Settings",
          security: "Security",
        };
        breadcrumbs.push({
          title: componentTitles[component] || component,
          href: `/profile?component=${component}`,
        });
      }
    } else if (location.pathname === "/billing") {
      breadcrumbs.push({ title: "Billing", href: "/billing" });
      if (component) {
        const componentTitles: Record<string, string> = {
          subscription: "Subscription",
          invoices: "Invoices",
          payment: "Payment Methods",
        };
        breadcrumbs.push({
          title: componentTitles[component] || component,
          href: `/billing?component=${component}`,
        });
      }
    } else if (location.pathname === "/terms&conditions") {
      breadcrumbs.push({
        title: "Terms & Conditions",
        href: "/terms&conditions",
      });
    } else if (location.pathname === "/privacypolicy") {
      breadcrumbs.push({ title: "Privacy Policy", href: "/privacypolicy" });
    } else if (location.pathname === "/refundpolicy") {
      breadcrumbs.push({ title: "Refund Policy", href: "/refundpolicy" });
    } else if (location.pathname === "/pricing") {
      breadcrumbs.push({ title: "Pricing", href: "/pricing" });
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <>
      <AppSidebar />
      <SidebarInset className="flex flex-col relative">
        <div className="absolute top-5 right-5 flex justify-center items-center">
          <NotificationBell />
        </div>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <div key={breadcrumb.href} className="flex items-center">
                    {index > 0 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                    <BreadcrumbItem className="hidden md:block">
                      {index === breadcrumbs.length - 1 ? (
                        <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink asChild>
                          <Link to={breadcrumb.href}>{breadcrumb.title}</Link>
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                  </div>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0 pb-12">
          {authenticated && isLoading ? (
            <div className="flex items-center justify-center flex-1">
              <Loader text="Loading application data..." size="lg" />
            </div>
          ) : (
            children
          )}
        </div>
        <div className="absolute bottom-0 left-0 right-0 py-1 text-sm font-thin bg-gray-100 border-t">
          <div className="flex justify-between px-3">
            <p className="text-muted-foreground">
              &copy; 2025 Smart Sensor Flow LLC
            </p>
            <p className="text-muted-foreground">v4.3.1.3PAAS</p>
          </div>
        </div>
      </SidebarInset>
    </>
  );
};

export default AppLayout;
