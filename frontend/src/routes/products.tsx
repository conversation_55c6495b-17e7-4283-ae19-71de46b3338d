import { createFileRoute } from "@tanstack/react-router";
import ProductsComponent from "../components/routes/products/ProductsComponent";

export const Route = createFileRoute("/products")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      component: (search.component as string) || "all-products",
    };
  },
  component: () => {
    const { component } = Route.useSearch();

    return (
      <div className="w-full h-full">
        <ProductsComponent activeComponent={component} />
      </div>
    );
  },
});
