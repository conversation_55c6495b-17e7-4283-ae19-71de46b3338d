import { createRootRoute, Outlet } from "@tanstack/react-router";
import { Toaster } from "../components/ui/sonner";
import { SidebarProvider } from "../components/ui/sidebar";
import { SecurityProvider } from "../context/SecurityWrapper";
import { StateProvider } from "../context/StateWrapper";
import AppLayout from "../components/layout/AppLayout";

export const Route = createRootRoute({
  component: () => (
    <SecurityProvider>
      <StateProvider>
        <SidebarProvider>
          <AppLayout>
            <Outlet />
          </AppLayout>
          <Toaster />
        </SidebarProvider>
      </StateProvider>
    </SecurityProvider>
  ),
});
