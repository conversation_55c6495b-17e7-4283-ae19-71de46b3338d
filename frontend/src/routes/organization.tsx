import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import OrganizationComponent from "../components/routes/organizations/OrganizationComponent";
import { CreateOrganisationDialog } from "../components/ui/custom/create-organisation-dialog";

export const Route = createFileRoute("/organization")({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      component: (search.component as string) || "dashboard",
    };
  },
  component: () => {
    const { component } = Route.useSearch();
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [dialogStep, setDialogStep] = useState(1);
    const navigate = useNavigate();

    // Handle payment dialog opening based on localStorage
    useEffect(() => {
      const showPaymentDialog =
        localStorage.getItem("showPaymentDialog") === "true";
      const paymentStep = parseInt(localStorage.getItem("paymentStep") || "1");

      console.log(
        "Organization route - localStorage showPaymentDialog:",
        showPaymentDialog,
        "paymentStep:",
        paymentStep
      );

      if (showPaymentDialog) {
        setDialogStep(paymentStep);
        setIsDialogOpen(true);

        // Clear localStorage after opening dialog
        localStorage.removeItem("showPaymentDialog");
        localStorage.removeItem("paymentStep");
      }
    }, []);

    // Handle dialog close
    const handleDialogOpenChange = (open: boolean) => {
      setIsDialogOpen(open);

      // Clear localStorage if dialog is closed
      if (!open) {
        localStorage.removeItem("showPaymentDialog");
        localStorage.removeItem("paymentStep");
      }
    };

    return (
      <div className="w-full h-full">
        <OrganizationComponent activeComponent={component} />

        {/* Payment dialog for continuing the payment flow */}
        <CreateOrganisationDialog
          open={isDialogOpen}
          onOpenChange={handleDialogOpenChange}
          onComplete={() => handleDialogOpenChange(false)}
          initialStep={dialogStep}
        />
      </div>
    );
  },
});
