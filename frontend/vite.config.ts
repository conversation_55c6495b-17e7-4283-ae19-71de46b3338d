import { defineConfig } from 'vite'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react-swc'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    TanStackRouterVite({ target: 'react', autoCodeSplitting: true }),
    react(),
    tailwindcss()
  ],
  server: {
    host: true, // or '0.0.0.0'
    port: 5174,
    allowedHosts: ['admin.local.ssf'],
    https: false, // or true if you're running Vite with HTTPS
  },
})
